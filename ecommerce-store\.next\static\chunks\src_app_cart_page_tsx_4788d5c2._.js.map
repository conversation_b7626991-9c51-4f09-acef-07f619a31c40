{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%2811%29/ecommerce-store/src/app/cart/page.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport Image from 'next/image';\n// import { Trash2, Minus, Plus, ShoppingBag } from 'lucide-react';\nimport { useCart } from '../context/CartContext';\n\nexport default function CartPage() {\n  const { items, removeFromCart, updateQuantity, getTotalPrice, clearCart } = useCart();\n  const [isVisible, setIsVisible] = React.useState(false);\n\n  React.useEffect(() => {\n    // Trigger animation after component mounts\n    const timer = setTimeout(() => setIsVisible(true), 100);\n    return () => clearTimeout(timer);\n  }, []);\n\n  if (items.length === 0) {\n    return (\n      <div className={`container mx-auto px-4 py-8 transition-all duration-700 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>\n        <div className=\"text-center py-16\">\n          <div className=\"animate-bounce-in\" style={{ animationDelay: '0.2s' }}>\n            <div className=\"text-8xl text-gray-300 mb-6\">🛒</div>\n          </div>\n          <div className=\"animate-slide-in-right\" style={{ animationDelay: '0.4s' }}>\n            <h1 className=\"text-2xl font-bold text-gray-800 mb-4\">Your cart is empty</h1>\n          </div>\n          <div className=\"animate-fade-in\" style={{ animationDelay: '0.6s' }}>\n            <p className=\"text-gray-600 mb-8\">\n              Looks like you haven't added any items to your cart yet.\n            </p>\n          </div>\n          <div className=\"animate-bounce-in\" style={{ animationDelay: '0.8s' }}>\n            <Link\n              href=\"/\"\n              className=\"bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 active:bg-blue-800 transition-all duration-200 transform hover:scale-105 active:scale-95 shadow-md hover:shadow-lg\"\n            >\n              Continue Shopping\n            </Link>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  const handleQuantityChange = (productId: string, newQuantity: number) => {\n    if (newQuantity <= 0) {\n      removeFromCart(productId);\n    } else {\n      updateQuantity(productId, newQuantity);\n    }\n  };\n\n  return (\n    <div className={`container mx-auto px-4 py-8 transition-all duration-700 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>\n      <div className=\"animate-slide-in-right\">\n        <h1 className=\"text-3xl font-bold text-gray-800 mb-8\">Shopping Cart</h1>\n      </div>\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n        {/* Cart Items */}\n        <div className=\"lg:col-span-2 space-y-4\">\n          {items.map((item, index) => (\n            <div\n              key={item.product.id}\n              className=\"bg-white rounded-lg shadow-md hover:shadow-lg p-6 flex flex-col sm:flex-row items-center space-y-4 sm:space-y-0 sm:space-x-6 animate-fade-in transform transition-all duration-300 hover:scale-[1.02]\"\n              style={{ animationDelay: `${index * 0.1}s` }}\n            >\n              <div className=\"relative w-24 h-24 flex-shrink-0\">\n                <Image\n                  src={item.product.image}\n                  alt={item.product.name}\n                  fill\n                  className=\"object-cover rounded-lg\"\n                />\n              </div>\n\n              <div className=\"flex-grow text-center sm:text-left\">\n                <h3 className=\"font-semibold text-gray-800 mb-2\">\n                  {item.product.name}\n                </h3>\n                <p className=\"text-gray-600 text-sm mb-2\">\n                  {item.product.category}\n                </p>\n                <p className=\"text-blue-600 font-bold\">\n                  ${item.product.price.toFixed(2)}\n                </p>\n              </div>\n\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"flex items-center border border-gray-300 rounded-lg overflow-hidden\">\n                  <button\n                    type=\"button\"\n                    onClick={() => handleQuantityChange(item.product.id, item.quantity - 1)}\n                    className=\"p-1.5 hover:bg-gray-100 active:bg-gray-200 transition-all duration-150 transform active:scale-95\"\n                    aria-label=\"Decrease quantity\"\n                  >\n                    <span className=\"text-sm\">−</span>\n                  </button>\n                  <span className=\"px-3 py-1.5 font-medium text-sm min-w-[2rem] text-center\" aria-label={`Quantity: ${item.quantity}`}>\n                    {item.quantity}\n                  </span>\n                  <button\n                    type=\"button\"\n                    onClick={() => handleQuantityChange(item.product.id, item.quantity + 1)}\n                    disabled={item.quantity >= item.product.stock}\n                    className=\"p-1.5 hover:bg-gray-100 active:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-150 transform active:scale-95\"\n                    aria-label=\"Increase quantity\"\n                  >\n                    <span className=\"text-sm\">+</span>\n                  </button>\n                </div>\n\n                <button\n                  type=\"button\"\n                  onClick={() => removeFromCart(item.product.id)}\n                  className=\"p-1.5 text-red-600 hover:bg-red-50 active:bg-red-100 rounded-lg transition-all duration-150 transform hover:scale-110 active:scale-95\"\n                  aria-label={`Remove ${item.product.name} from cart`}\n                >\n                  <span className=\"text-sm\">🗑️</span>\n                </button>\n              </div>\n\n              <div className=\"text-right\">\n                <p className=\"font-bold text-gray-800\">\n                  ${(item.product.price * item.quantity).toFixed(2)}\n                </p>\n              </div>\n            </div>\n          ))}\n\n          <div className=\"flex justify-between items-center pt-4\">\n            <button\n              type=\"button\"\n              onClick={clearCart}\n              className=\"text-red-600 hover:text-red-700 font-medium transition-all duration-200 px-3 py-1 rounded-md hover:bg-red-50 active:bg-red-100 transform active:scale-95\"\n            >\n              Clear Cart\n            </button>\n            <Link\n              href=\"/\"\n              className=\"text-blue-600 hover:text-blue-700 font-medium transition-all duration-200 px-3 py-1 rounded-md hover:bg-blue-50 active:bg-blue-100 transform hover:scale-105 active:scale-95\"\n            >\n              Continue Shopping\n            </Link>\n          </div>\n        </div>\n\n        {/* Order Summary */}\n        <div className=\"bg-white rounded-lg shadow-md hover:shadow-lg p-6 h-fit animate-slide-in-right transform transition-all duration-300\" style={{ animationDelay: '0.3s' }}>\n          <h2 className=\"text-xl font-bold text-gray-800 mb-6\">Order Summary</h2>\n\n          <div className=\"space-y-4\">\n            <div className=\"flex justify-between\">\n              <span className=\"text-gray-600\">Subtotal</span>\n              <span className=\"font-medium\">${getTotalPrice().toFixed(2)}</span>\n            </div>\n            <div className=\"flex justify-between\">\n              <span className=\"text-gray-600\">Shipping</span>\n              <span className=\"font-medium\">Free</span>\n            </div>\n            <div className=\"flex justify-between\">\n              <span className=\"text-gray-600\">Tax</span>\n              <span className=\"font-medium\">${(getTotalPrice() * 0.08).toFixed(2)}</span>\n            </div>\n            <hr />\n            <div className=\"flex justify-between text-lg font-bold\">\n              <span>Total</span>\n              <span>${(getTotalPrice() * 1.08).toFixed(2)}</span>\n            </div>\n          </div>\n\n          <button\n            type=\"button\"\n            className=\"w-full bg-blue-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-blue-700 active:bg-blue-800 transition-all duration-200 mt-6 transform hover:scale-105 active:scale-95 shadow-md hover:shadow-lg\"\n            aria-label={`Proceed to checkout with total of $${(getTotalPrice() * 1.08).toFixed(2)}`}\n          >\n            Proceed to Checkout\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA,mEAAmE;AACnE;;;AANA;;;;;AAQe,SAAS;;IACtB,MAAM,EAAE,KAAK,EAAE,cAAc,EAAE,cAAc,EAAE,aAAa,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,UAAO,AAAD;IAClF,MAAM,CAAC,WAAW,aAAa,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAEjD,6JAAA,CAAA,UAAK,CAAC,SAAS;8BAAC;YACd,2CAA2C;YAC3C,MAAM,QAAQ;4CAAW,IAAM,aAAa;2CAAO;YACnD;sCAAO,IAAM,aAAa;;QAC5B;6BAAG,EAAE;IAEL,IAAI,MAAM,MAAM,KAAK,GAAG;QACtB,qBACE,6LAAC;YAAI,WAAW,CAAC,wDAAwD,EAAE,YAAY,8BAA8B,2BAA2B;sBAC9I,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;wBAAoB,OAAO;4BAAE,gBAAgB;wBAAO;kCACjE,cAAA,6LAAC;4BAAI,WAAU;sCAA8B;;;;;;;;;;;kCAE/C,6LAAC;wBAAI,WAAU;wBAAyB,OAAO;4BAAE,gBAAgB;wBAAO;kCACtE,cAAA,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;;;;;;kCAExD,6LAAC;wBAAI,WAAU;wBAAkB,OAAO;4BAAE,gBAAgB;wBAAO;kCAC/D,cAAA,6LAAC;4BAAE,WAAU;sCAAqB;;;;;;;;;;;kCAIpC,6LAAC;wBAAI,WAAU;wBAAoB,OAAO;4BAAE,gBAAgB;wBAAO;kCACjE,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;IAOX;IAEA,MAAM,uBAAuB,CAAC,WAAmB;QAC/C,IAAI,eAAe,GAAG;YACpB,eAAe;QACjB,OAAO;YACL,eAAe,WAAW;QAC5B;IACF;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,wDAAwD,EAAE,YAAY,8BAA8B,2BAA2B;;0BAC9I,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAG,WAAU;8BAAwC;;;;;;;;;;;0BAGxD,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;4BACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC;oCAEC,WAAU;oCACV,OAAO;wCAAE,gBAAgB,GAAG,QAAQ,IAAI,CAAC,CAAC;oCAAC;;sDAE3C,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;gDACJ,KAAK,KAAK,OAAO,CAAC,KAAK;gDACvB,KAAK,KAAK,OAAO,CAAC,IAAI;gDACtB,IAAI;gDACJ,WAAU;;;;;;;;;;;sDAId,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DACX,KAAK,OAAO,CAAC,IAAI;;;;;;8DAEpB,6LAAC;oDAAE,WAAU;8DACV,KAAK,OAAO,CAAC,QAAQ;;;;;;8DAExB,6LAAC;oDAAE,WAAU;;wDAA0B;wDACnC,KAAK,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC;;;;;;;;;;;;;sDAIjC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DACC,MAAK;4DACL,SAAS,IAAM,qBAAqB,KAAK,OAAO,CAAC,EAAE,EAAE,KAAK,QAAQ,GAAG;4DACrE,WAAU;4DACV,cAAW;sEAEX,cAAA,6LAAC;gEAAK,WAAU;0EAAU;;;;;;;;;;;sEAE5B,6LAAC;4DAAK,WAAU;4DAA2D,cAAY,CAAC,UAAU,EAAE,KAAK,QAAQ,EAAE;sEAChH,KAAK,QAAQ;;;;;;sEAEhB,6LAAC;4DACC,MAAK;4DACL,SAAS,IAAM,qBAAqB,KAAK,OAAO,CAAC,EAAE,EAAE,KAAK,QAAQ,GAAG;4DACrE,UAAU,KAAK,QAAQ,IAAI,KAAK,OAAO,CAAC,KAAK;4DAC7C,WAAU;4DACV,cAAW;sEAEX,cAAA,6LAAC;gEAAK,WAAU;0EAAU;;;;;;;;;;;;;;;;;8DAI9B,6LAAC;oDACC,MAAK;oDACL,SAAS,IAAM,eAAe,KAAK,OAAO,CAAC,EAAE;oDAC7C,WAAU;oDACV,cAAY,CAAC,OAAO,EAAE,KAAK,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC;8DAEnD,cAAA,6LAAC;wDAAK,WAAU;kEAAU;;;;;;;;;;;;;;;;;sDAI9B,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAE,WAAU;;oDAA0B;oDACnC,CAAC,KAAK,OAAO,CAAC,KAAK,GAAG,KAAK,QAAQ,EAAE,OAAO,CAAC;;;;;;;;;;;;;mCA7D9C,KAAK,OAAO,CAAC,EAAE;;;;;0CAmExB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,SAAS;wCACT,WAAU;kDACX;;;;;;kDAGD,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;kCAOL,6LAAC;wBAAI,WAAU;wBAAuH,OAAO;4BAAE,gBAAgB;wBAAO;;0CACpK,6LAAC;gCAAG,WAAU;0CAAuC;;;;;;0CAErD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,6LAAC;gDAAK,WAAU;;oDAAc;oDAAE,gBAAgB,OAAO,CAAC;;;;;;;;;;;;;kDAE1D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,6LAAC;gDAAK,WAAU;0DAAc;;;;;;;;;;;;kDAEhC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,6LAAC;gDAAK,WAAU;;oDAAc;oDAAE,CAAC,kBAAkB,IAAI,EAAE,OAAO,CAAC;;;;;;;;;;;;;kDAEnE,6LAAC;;;;;kDACD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;0DAAK;;;;;;0DACN,6LAAC;;oDAAK;oDAAE,CAAC,kBAAkB,IAAI,EAAE,OAAO,CAAC;;;;;;;;;;;;;;;;;;;0CAI7C,6LAAC;gCACC,MAAK;gCACL,WAAU;gCACV,cAAY,CAAC,mCAAmC,EAAE,CAAC,kBAAkB,IAAI,EAAE,OAAO,CAAC,IAAI;0CACxF;;;;;;;;;;;;;;;;;;;;;;;;AAOX;GAhLwB;;QACsD,wIAAA,CAAA,UAAO;;;KAD7D", "debugId": null}}]}