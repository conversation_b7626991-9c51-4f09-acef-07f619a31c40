'use client';

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
// import { Trash2, Minus, Plus, ShoppingBag } from 'lucide-react';
import { useCart } from '../context/CartContext';

export default function CartPage() {
  const { items, removeFromCart, updateQuantity, getTotalPrice, clearCart } = useCart();
  const [isVisible, setIsVisible] = React.useState(false);

  React.useEffect(() => {
    // Trigger animation after component mounts
    const timer = setTimeout(() => setIsVisible(true), 100);
    return () => clearTimeout(timer);
  }, []);

  if (items.length === 0) {
    return (
      <div className={`container mx-auto px-4 py-8 transition-all duration-700 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
        <div className="text-center py-16">
          <div className="animate-bounce-in" style={{ animationDelay: '0.2s' }}>
            <div className="text-8xl text-gray-300 mb-6">🛒</div>
          </div>
          <div className="animate-slide-in-right" style={{ animationDelay: '0.4s' }}>
            <h1 className="text-2xl font-bold text-gray-800 mb-4">Your cart is empty</h1>
          </div>
          <div className="animate-fade-in" style={{ animationDelay: '0.6s' }}>
            <p className="text-gray-600 mb-8">
              Looks like you haven't added any items to your cart yet.
            </p>
          </div>
          <div className="animate-bounce-in" style={{ animationDelay: '0.8s' }}>
            <Link
              href="/"
              className="bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 active:bg-blue-800 transition-all duration-200 transform hover:scale-105 active:scale-95 shadow-md hover:shadow-lg"
            >
              Continue Shopping
            </Link>
          </div>
        </div>
      </div>
    );
  }

  const handleQuantityChange = (productId: string, newQuantity: number) => {
    if (newQuantity <= 0) {
      removeFromCart(productId);
    } else {
      updateQuantity(productId, newQuantity);
    }
  };

  return (
    <div className={`container mx-auto px-4 py-8 transition-all duration-700 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
      <div className="animate-slide-in-right">
        <h1 className="text-3xl font-bold text-gray-800 mb-8">Shopping Cart</h1>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Cart Items */}
        <div className="lg:col-span-2 space-y-4">
          {items.map((item, index) => (
            <div
              key={item.product.id}
              className="bg-white rounded-lg shadow-md hover:shadow-lg p-6 flex flex-col sm:flex-row items-center space-y-4 sm:space-y-0 sm:space-x-6 animate-fade-in transform transition-all duration-300 hover:scale-[1.02]"
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <div className="relative w-24 h-24 flex-shrink-0">
                <Image
                  src={item.product.image}
                  alt={item.product.name}
                  fill
                  className="object-cover rounded-lg"
                />
              </div>

              <div className="flex-grow text-center sm:text-left">
                <h3 className="font-semibold text-gray-800 mb-2">
                  {item.product.name}
                </h3>
                <p className="text-gray-600 text-sm mb-2">
                  {item.product.category}
                </p>
                <p className="text-blue-600 font-bold">
                  ${item.product.price.toFixed(2)}
                </p>
              </div>

              <div className="flex items-center space-x-3">
                <div className="flex items-center border border-gray-300 rounded-lg overflow-hidden">
                  <button
                    type="button"
                    onClick={() => handleQuantityChange(item.product.id, item.quantity - 1)}
                    className="p-1.5 hover:bg-gray-100 active:bg-gray-200 transition-all duration-150 transform active:scale-95"
                    aria-label="Decrease quantity"
                  >
                    <span className="text-sm">−</span>
                  </button>
                  <span className="px-3 py-1.5 font-medium text-sm min-w-[2rem] text-center" aria-label={`Quantity: ${item.quantity}`}>
                    {item.quantity}
                  </span>
                  <button
                    type="button"
                    onClick={() => handleQuantityChange(item.product.id, item.quantity + 1)}
                    disabled={item.quantity >= item.product.stock}
                    className="p-1.5 hover:bg-gray-100 active:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-150 transform active:scale-95"
                    aria-label="Increase quantity"
                  >
                    <span className="text-sm">+</span>
                  </button>
                </div>

                <button
                  type="button"
                  onClick={() => removeFromCart(item.product.id)}
                  className="p-1.5 text-red-600 hover:bg-red-50 active:bg-red-100 rounded-lg transition-all duration-150 transform hover:scale-110 active:scale-95"
                  aria-label={`Remove ${item.product.name} from cart`}
                >
                  <span className="text-sm">🗑️</span>
                </button>
              </div>

              <div className="text-right">
                <p className="font-bold text-gray-800">
                  ${(item.product.price * item.quantity).toFixed(2)}
                </p>
              </div>
            </div>
          ))}

          <div className="flex justify-between items-center pt-4">
            <button
              type="button"
              onClick={clearCart}
              className="text-red-600 hover:text-red-700 font-medium transition-all duration-200 px-3 py-1 rounded-md hover:bg-red-50 active:bg-red-100 transform active:scale-95"
            >
              Clear Cart
            </button>
            <Link
              href="/"
              className="text-blue-600 hover:text-blue-700 font-medium transition-all duration-200 px-3 py-1 rounded-md hover:bg-blue-50 active:bg-blue-100 transform hover:scale-105 active:scale-95"
            >
              Continue Shopping
            </Link>
          </div>
        </div>

        {/* Order Summary */}
        <div className="bg-white rounded-lg shadow-md hover:shadow-lg p-6 h-fit animate-slide-in-right transform transition-all duration-300" style={{ animationDelay: '0.3s' }}>
          <h2 className="text-xl font-bold text-gray-800 mb-6">Order Summary</h2>

          <div className="space-y-4">
            <div className="flex justify-between">
              <span className="text-gray-600">Subtotal</span>
              <span className="font-medium">${getTotalPrice().toFixed(2)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Shipping</span>
              <span className="font-medium">Free</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Tax</span>
              <span className="font-medium">${(getTotalPrice() * 0.08).toFixed(2)}</span>
            </div>
            <hr />
            <div className="flex justify-between text-lg font-bold">
              <span>Total</span>
              <span>${(getTotalPrice() * 1.08).toFixed(2)}</span>
            </div>
          </div>

          <button
            type="button"
            className="w-full bg-blue-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-blue-700 active:bg-blue-800 transition-all duration-200 mt-6 transform hover:scale-105 active:scale-95 shadow-md hover:shadow-lg"
            aria-label={`Proceed to checkout with total of $${(getTotalPrice() * 1.08).toFixed(2)}`}
          >
            Proceed to Checkout
          </button>
        </div>
      </div>
    </div>
  );
}
