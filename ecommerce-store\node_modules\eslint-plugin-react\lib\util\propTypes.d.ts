declare function _exports(context: any, components: any, utils: any): {
    ClassExpression(node: any): void;
    ClassDeclaration(node: any): void;
    'ClassProperty, PropertyDefinition'(node: any): void;
    ObjectExpression(node: any): void;
    FunctionExpression(node: any): void;
    ImportDeclaration(node: any): void;
    FunctionDeclaration: (node: ASTNode, rootNode: ASTNode) => void;
    ArrowFunctionExpression: (node: ASTNode, rootNode: ASTNode) => void;
    MemberExpression(node: any): void;
    MethodDefinition(node: any): void;
    TypeAlias(node: any): void;
    TypeParameterDeclaration(node: any): void;
    Program(): void;
    BlockStatement(): void;
    'BlockStatement:exit'(): void;
    'Program:exit'(): void;
};
export = _exports;
//# sourceMappingURL=propTypes.d.ts.map