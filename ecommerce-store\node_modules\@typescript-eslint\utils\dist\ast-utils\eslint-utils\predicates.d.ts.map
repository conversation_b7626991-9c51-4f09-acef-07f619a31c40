{"version": 3, "file": "predicates.d.ts", "sourceRoot": "", "sources": ["../../../src/ast-utils/eslint-utils/predicates.ts"], "names": [], "mappings": "AAEA,OAAO,KAAK,EAAE,QAAQ,EAAE,MAAM,iBAAiB,CAAC;AAEhD,KAAK,uBAAuB,CAAC,aAAa,SAAS,QAAQ,CAAC,KAAK,IAAI,CACnE,KAAK,EAAE,QAAQ,CAAC,KAAK,KAClB,KAAK,IAAI,aAAa,CAAC;AAE5B,KAAK,0BAA0B,CAAC,aAAa,SAAS,QAAQ,CAAC,KAAK,IAAI,CACtE,KAAK,EAAE,QAAQ,CAAC,KAAK,KAClB,KAAK,IAAI,OAAO,CAAC,QAAQ,CAAC,KAAK,EAAE,aAAa,CAAC,CAAC;AAErD,KAAK,wBAAwB,CAAC,KAAK,SAAS,MAAM,IAAI;IACpD,KAAK,EAAE,KAAK,CAAC;CACd,GAAG,QAAQ,CAAC,eAAe,CAAC;AAC7B,KAAK,kCAAkC,CAAC,KAAK,SAAS,MAAM,IAC1D,uBAAuB,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC,CAAC;AAC3D,KAAK,qCAAqC,CAAC,KAAK,SAAS,MAAM,IAC7D,0BAA0B,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC,CAAC;AAE9D,eAAO,MAAM,YAAY,EACK,kCAAkC,CAAC,IAAI,CAAC,CAAC;AACvE,eAAO,MAAM,eAAe,EACK,qCAAqC,CAAC,IAAI,CAAC,CAAC;AAE7E,eAAO,MAAM,mBAAmB,EACK,kCAAkC,CAAC,GAAG,CAAC,CAAC;AAC7E,eAAO,MAAM,sBAAsB,EACK,qCAAqC,CAAC,GAAG,CAAC,CAAC;AAEnF,eAAO,MAAM,qBAAqB,EACK,kCAAkC,CAAC,GAAG,CAAC,CAAC;AAC/E,eAAO,MAAM,wBAAwB,EACK,qCAAqC,CAAC,GAAG,CAAC,CAAC;AAErF,eAAO,MAAM,mBAAmB,EACK,kCAAkC,CAAC,GAAG,CAAC,CAAC;AAC7E,eAAO,MAAM,sBAAsB,EACK,qCAAqC,CAAC,GAAG,CAAC,CAAC;AAEnF,eAAO,MAAM,YAAY,EACK,kCAAkC,CAAC,GAAG,CAAC,CAAC;AACtE,eAAO,MAAM,eAAe,EACK,qCAAqC,CAAC,GAAG,CAAC,CAAC;AAE5E,eAAO,MAAM,YAAY,EACK,kCAAkC,CAAC,GAAG,CAAC,CAAC;AACtE,eAAO,MAAM,eAAe,EACK,qCAAqC,CAAC,GAAG,CAAC,CAAC;AAE5E,eAAO,MAAM,cAAc,EACK,uBAAuB,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;AAC1E,eAAO,MAAM,iBAAiB,EACK,0BAA0B,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;AAEhF,eAAO,MAAM,mBAAmB,EACK,kCAAkC,CAAC,GAAG,CAAC,CAAC;AAC7E,eAAO,MAAM,sBAAsB,EACK,qCAAqC,CAAC,GAAG,CAAC,CAAC;AAEnF,eAAO,MAAM,qBAAqB,EACK,kCAAkC,CAAC,GAAG,CAAC,CAAC;AAC/E,eAAO,MAAM,wBAAwB,EACK,qCAAqC,CAAC,GAAG,CAAC,CAAC;AAErF,eAAO,MAAM,mBAAmB,EACK,kCAAkC,CAAC,GAAG,CAAC,CAAC;AAC7E,eAAO,MAAM,sBAAsB,EACK,qCAAqC,CAAC,GAAG,CAAC,CAAC;AAEnF,eAAO,MAAM,gBAAgB,EACK,kCAAkC,CAAC,GAAG,CAAC,CAAC;AAC1E,eAAO,MAAM,mBAAmB,EACK,qCAAqC,CAAC,GAAG,CAAC,CAAC"}