{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%2811%29/ecommerce-store/src/app/not-found.tsx"], "sourcesContent": ["import React from 'react';\nimport Link from 'next/link';\n// import { Search, Home, ArrowLeft } from 'lucide-react';\n\nexport default function NotFound() {\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gray-50 px-4\">\n      <div className=\"max-w-md w-full text-center\">\n        <div className=\"mb-8\">\n          <h1 className=\"text-9xl font-bold text-blue-600 mb-4\">404</h1>\n          <h2 className=\"text-2xl font-bold text-gray-800 mb-2\">Page Not Found</h2>\n          <p className=\"text-gray-600\">\n            Sorry, we couldn't find the page you're looking for. It might have been moved, deleted, or you entered the wrong URL.\n          </p>\n        </div>\n\n        <div className=\"space-y-4\">\n          <Link\n            href=\"/\"\n            className=\"w-full bg-blue-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-blue-700 active:bg-blue-800 transition-all duration-200 flex items-center justify-center space-x-2 transform hover:scale-105 active:scale-95 shadow-md hover:shadow-lg\"\n          >\n            <span>🏠 Go to Homepage</span>\n          </Link>\n          \n          <Link\n            href=\"/search\"\n            className=\"w-full bg-gray-200 text-gray-800 py-3 px-6 rounded-lg font-medium hover:bg-gray-300 active:bg-gray-400 transition-all duration-200 flex items-center justify-center space-x-2 transform hover:scale-105 active:scale-95\"\n          >\n            <span>🔍 Search Products</span>\n          </Link>\n          \n          <button\n            type=\"button\"\n            onClick={() => window.history.back()}\n            className=\"w-full bg-white text-gray-600 py-3 px-6 rounded-lg font-medium hover:bg-gray-50 active:bg-gray-100 transition-all duration-200 flex items-center justify-center space-x-2 transform hover:scale-105 active:scale-95 border border-gray-300\"\n          >\n            <span>← Go Back</span>\n          </button>\n        </div>\n\n        <div className=\"mt-8 text-sm text-gray-500\">\n          <p>Need help? Contact our support team.</p>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;;;AAGe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;8BAK/B,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;sCAEV,cAAA,8OAAC;0CAAK;;;;;;;;;;;sCAGR,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;sCAEV,cAAA,8OAAC;0CAAK;;;;;;;;;;;sCAGR,8OAAC;4BACC,MAAK;4BACL,SAAS,IAAM,OAAO,OAAO,CAAC,IAAI;4BAClC,WAAU;sCAEV,cAAA,8OAAC;0CAAK;;;;;;;;;;;;;;;;;8BAIV,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;kCAAE;;;;;;;;;;;;;;;;;;;;;;AAKb", "debugId": null}}]}