import React from 'react';
import Link from 'next/link';
// import { Search, Home, ArrowLeft } from 'lucide-react';

export default function NotFound() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 px-4">
      <div className="max-w-md w-full text-center">
        <div className="mb-8">
          <h1 className="text-9xl font-bold text-blue-600 mb-4">404</h1>
          <h2 className="text-2xl font-bold text-gray-800 mb-2">Page Not Found</h2>
          <p className="text-gray-600">
            Sorry, we couldn't find the page you're looking for. It might have been moved, deleted, or you entered the wrong URL.
          </p>
        </div>

        <div className="space-y-4">
          <Link
            href="/"
            className="w-full bg-blue-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-blue-700 active:bg-blue-800 transition-all duration-200 flex items-center justify-center space-x-2 transform hover:scale-105 active:scale-95 shadow-md hover:shadow-lg"
          >
            <span>🏠 Go to Homepage</span>
          </Link>
          
          <Link
            href="/search"
            className="w-full bg-gray-200 text-gray-800 py-3 px-6 rounded-lg font-medium hover:bg-gray-300 active:bg-gray-400 transition-all duration-200 flex items-center justify-center space-x-2 transform hover:scale-105 active:scale-95"
          >
            <span>🔍 Search Products</span>
          </Link>
          
          <button
            type="button"
            onClick={() => window.history.back()}
            className="w-full bg-white text-gray-600 py-3 px-6 rounded-lg font-medium hover:bg-gray-50 active:bg-gray-100 transition-all duration-200 flex items-center justify-center space-x-2 transform hover:scale-105 active:scale-95 border border-gray-300"
          >
            <span>← Go Back</span>
          </button>
        </div>

        <div className="mt-8 text-sm text-gray-500">
          <p>Need help? Contact our support team.</p>
        </div>
      </div>
    </div>
  );
}
