'use client';

import React from 'react';
import Link from 'next/link';
import { Star, ShoppingCart } from 'lucide-react';
import { Product } from '../types';
import { useCart } from '../context/CartContext';
import { useToast } from '../context/ToastContext';
import ReliableImage from './ReliableImage';

interface ProductCardProps {
  product: Product;
}

const ProductCard: React.FC<ProductCardProps> = ({ product }) => {
  const { addToCart } = useCart();
  const { showToast } = useToast();
  const [isAdding, setIsAdding] = React.useState(false);

  const handleAddToCart = React.useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsAdding(true);
    addToCart(product);

    // Show toast notification
    showToast(`${product.name} added to cart!`, 'success', 2000);

    // Reset animation state after animation completes
    setTimeout(() => setIsAdding(false), 600);
  }, [product, addToCart, showToast]);

  const renderStars = (rating: number) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 0; i < fullStars; i++) {
      stars.push(
        <Star key={i} className="h-4 w-4 fill-yellow-400 text-yellow-400" />
      );
    }

    if (hasHalfStar) {
      stars.push(
        <Star key="half" className="h-4 w-4 fill-yellow-400 text-yellow-400 opacity-50" />
      );
    }

    const emptyStars = 5 - Math.ceil(rating);
    for (let i = 0; i < emptyStars; i++) {
      stars.push(
        <Star key={`empty-${i}`} className="h-4 w-4 text-gray-300" />
      );
    }

    return stars;
  };

  return (
    <div className="bg-white rounded-lg shadow-md hover:shadow-xl transition-all duration-300 overflow-hidden group transform hover:-translate-y-1">
      <Link href={`/product/${product.id}`} className="block">
        <div className="relative aspect-square overflow-hidden">
          <ReliableImage
            src={product.image}
            alt={product.name}
            width={500}
            height={500}
            className="object-cover group-hover:scale-110 transition-transform duration-500 w-full h-full"
            fallbackSrc="https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=500&h=500&fit=crop"
          />
          {product.stock < 10 && product.stock > 0 && (
            <div className="absolute top-2 left-2 bg-orange-500 text-white px-2 py-1 rounded-full text-xs font-medium animate-pulse">
              Only {product.stock} left
            </div>
          )}
          {product.stock === 0 && (
            <div className="absolute top-2 left-2 bg-red-500 text-white px-2 py-1 rounded-full text-xs font-medium">
              Out of Stock
            </div>
          )}
          <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 transition-all duration-300" />
        </div>
      </Link>

      <div className="p-4">
        <Link href={`/product/${product.id}`} className="block">
          <h3 className="font-semibold text-gray-800 mb-2 line-clamp-2 group-hover:text-blue-600 transition-colors duration-200">
            {product.name}
          </h3>

          <p className="text-gray-600 text-sm mb-3 line-clamp-2">
            {product.description}
          </p>

          <div className="flex items-center mb-3">
            <div className="flex items-center" role="img" aria-label={`Rating: ${product.rating} out of 5 stars`}>
              {renderStars(product.rating)}
            </div>
            <span className="text-sm text-gray-500 ml-2">
              ({product.reviews} reviews)
            </span>
          </div>
        </Link>

        <div className="flex items-center justify-between">
          <span className="text-lg font-bold text-blue-600">
            ${product.price.toFixed(2)}
          </span>

          <button
            type="button"
            onClick={handleAddToCart}
            disabled={product.stock === 0}
            className={`bg-blue-600 text-white px-3 py-1.5 rounded-md hover:bg-blue-700 active:bg-blue-800 transition-all duration-200 disabled:bg-gray-400 disabled:cursor-not-allowed flex items-center space-x-1.5 text-sm font-medium transform hover:scale-105 active:scale-95 shadow-md hover:shadow-lg ${
              isAdding ? 'animate-cart-bounce bg-green-600' : ''
            }`}
            aria-label={`Add ${product.name} to cart`}
          >
            <ShoppingCart className={`h-3.5 w-3.5 transition-all duration-200 ${isAdding ? 'animate-cart-shake' : ''}`} />
            <span className={isAdding ? 'animate-pulse' : ''}>{isAdding ? 'Added!' : 'Add'}</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default React.memo(ProductCard);
