{"types": {"problem": [], "suggestion": [], "layout": []}, "deprecated": [], "removed": [{"removed": "generator-star", "replacedBy": [{"rule": {"name": "generator-star-spacing"}}]}, {"removed": "global-strict", "replacedBy": [{"rule": {"name": "strict"}}]}, {"removed": "no-arrow-condition", "replacedBy": [{"rule": {"name": "no-confusing-arrow"}}, {"rule": {"name": "no-constant-condition"}}]}, {"removed": "no-comma-dangle", "replacedBy": [{"rule": {"name": "comma-dangle"}}]}, {"removed": "no-empty-class", "replacedBy": [{"rule": {"name": "no-empty-character-class"}}]}, {"removed": "no-empty-label", "replacedBy": [{"rule": {"name": "no-labels"}}]}, {"removed": "no-extra-strict", "replacedBy": [{"rule": {"name": "strict"}}]}, {"removed": "no-reserved-keys", "replacedBy": [{"rule": {"name": "quote-props"}}]}, {"removed": "no-space-before-semi", "replacedBy": [{"rule": {"name": "semi-spacing"}}]}, {"removed": "no-wrap-func", "replacedBy": [{"rule": {"name": "no-extra-parens"}}]}, {"removed": "space-after-function-name", "replacedBy": [{"rule": {"name": "space-before-function-paren"}}]}, {"removed": "space-after-keywords", "replacedBy": [{"rule": {"name": "keyword-spacing"}}]}, {"removed": "space-before-function-parentheses", "replacedBy": [{"rule": {"name": "space-before-function-paren"}}]}, {"removed": "space-before-keywords", "replacedBy": [{"rule": {"name": "keyword-spacing"}}]}, {"removed": "space-in-brackets", "replacedBy": [{"rule": {"name": "object-curly-spacing"}}, {"rule": {"name": "array-bracket-spacing"}}, {"rule": {"name": "computed-property-spacing"}}]}, {"removed": "space-return-throw-case", "replacedBy": [{"rule": {"name": "keyword-spacing"}}]}, {"removed": "space-unary-word-ops", "replacedBy": [{"rule": {"name": "space-unary-ops"}}]}, {"removed": "spaced-line-comment", "replacedBy": [{"rule": {"name": "spaced-comment"}}]}, {"removed": "valid-jsdoc", "replacedBy": []}, {"removed": "require-jsdoc", "replacedBy": []}]}