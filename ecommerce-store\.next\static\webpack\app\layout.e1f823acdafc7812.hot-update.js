"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/data/products.ts":
/*!**********************************!*\
  !*** ./src/app/data/products.ts ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   categories: () => (/* binding */ categories),\n/* harmony export */   products: () => (/* binding */ products)\n/* harmony export */ });\nconst categories = [\n    {\n        id: '1',\n        name: 'Electronics',\n        slug: 'electronics'\n    },\n    {\n        id: '2',\n        name: 'Clothing',\n        slug: 'clothing'\n    },\n    {\n        id: '3',\n        name: 'Books',\n        slug: 'books'\n    },\n    {\n        id: '4',\n        name: 'Home & Garden',\n        slug: 'home-garden'\n    },\n    {\n        id: '5',\n        name: 'Sports',\n        slug: 'sports'\n    }\n];\nconst products = [\n    {\n        id: '1',\n        name: 'Wireless Bluetooth Headphones',\n        price: 79.99,\n        description: 'High-quality wireless headphones with noise cancellation and 30-hour battery life.',\n        image: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=500&h=500&fit=crop',\n        category: 'Electronics',\n        stock: 25,\n        rating: 4.5,\n        reviews: 128\n    },\n    {\n        id: '2',\n        name: 'Smartphone Case',\n        price: 24.99,\n        description: 'Durable protective case for smartphones with shock absorption and wireless charging support.',\n        image: 'https://images.unsplash.com/photo-1556656793-08538906a9f8?w=500&h=500&fit=crop',\n        category: 'Electronics',\n        stock: 50,\n        rating: 4.2,\n        reviews: 89\n    },\n    {\n        id: '3',\n        name: 'Laptop Stand',\n        price: 45.99,\n        description: 'Adjustable aluminum laptop stand with cooling design and ergonomic positioning.',\n        image: 'https://images.unsplash.com/photo-1527864550417-7fd91fc51a46?w=500&h=500&fit=crop',\n        category: 'Electronics',\n        stock: 35,\n        rating: 4.6,\n        reviews: 156\n    },\n    {\n        id: '4',\n        name: 'Wireless Mouse',\n        price: 29.99,\n        description: 'Ergonomic wireless mouse with precision tracking and long battery life.',\n        image: 'https://images.unsplash.com/photo-1527814050087-3793815479db?w=500&h=500&fit=crop',\n        category: 'Electronics',\n        stock: 60,\n        rating: 4.3,\n        reviews: 201\n    },\n    {\n        id: '5',\n        name: 'Cotton T-Shirt',\n        price: 19.99,\n        description: 'Comfortable 100% cotton t-shirt available in multiple colors and sizes.',\n        image: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=500&h=500&fit=crop',\n        category: 'Clothing',\n        stock: 100,\n        rating: 4.3,\n        reviews: 245\n    },\n    {\n        id: '6',\n        name: 'Denim Jeans',\n        price: 59.99,\n        description: 'Classic fit denim jeans made from premium quality fabric.',\n        image: 'https://images.unsplash.com/photo-1542272604-787c3835535d?w=500&h=500&fit=crop',\n        category: 'Clothing',\n        stock: 75,\n        rating: 4.4,\n        reviews: 156\n    },\n    {\n        id: '7',\n        name: 'Hoodie',\n        price: 49.99,\n        description: 'Cozy fleece hoodie with kangaroo pocket and adjustable drawstring.',\n        image: 'https://images.unsplash.com/photo-1556821840-3a63f95609a7?w=500&h=500&fit=crop',\n        category: 'Clothing',\n        stock: 45,\n        rating: 4.5,\n        reviews: 189\n    },\n    {\n        id: '8',\n        name: 'Sneakers',\n        price: 89.99,\n        description: 'Comfortable running sneakers with breathable mesh and cushioned sole.',\n        image: 'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=500&h=500&fit=crop',\n        category: 'Clothing',\n        stock: 30,\n        rating: 4.7,\n        reviews: 312\n    },\n    {\n        id: '9',\n        name: 'Programming Book',\n        price: 39.99,\n        description: 'Comprehensive guide to modern web development with practical examples.',\n        image: 'https://images.unsplash.com/photo-1544716278-ca5e3f4abd8c?w=500&h=500&fit=crop',\n        category: 'Books',\n        stock: 30,\n        rating: 4.7,\n        reviews: 92\n    },\n    {\n        id: '10',\n        name: 'Design Thinking Book',\n        price: 34.99,\n        description: 'Learn the principles of design thinking and user experience design.',\n        image: 'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=500&h=500&fit=crop',\n        category: 'Books',\n        stock: 25,\n        rating: 4.4,\n        reviews: 78\n    },\n    {\n        id: '11',\n        name: 'Business Strategy Book',\n        price: 42.99,\n        description: 'Essential strategies for modern business leadership and growth.',\n        image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=500&h=500&fit=crop',\n        category: 'Books',\n        stock: 40,\n        rating: 4.2,\n        reviews: 134\n    },\n    {\n        id: '12',\n        name: 'Coffee Mug',\n        price: 12.99,\n        description: 'Ceramic coffee mug with ergonomic handle and heat retention design.',\n        image: 'https://images.unsplash.com/photo-1541167760496-1628856ab772?w=500&h=500&fit=crop',\n        category: 'Home & Garden',\n        stock: 200,\n        rating: 4.1,\n        reviews: 67\n    },\n    {\n        id: '13',\n        name: 'Plant Pot',\n        price: 18.99,\n        description: 'Modern ceramic plant pot with drainage system, perfect for indoor plants.',\n        image: 'https://images.unsplash.com/photo-1485955900006-10f4d324d411?w=500&h=500&fit=crop',\n        category: 'Home & Garden',\n        stock: 85,\n        rating: 4.3,\n        reviews: 156\n    },\n    {\n        id: '14',\n        name: 'Desk Lamp',\n        price: 35.99,\n        description: 'LED desk lamp with adjustable brightness and USB charging port.',\n        image: 'https://images.unsplash.com/photo-1507473885765-e6ed057f782c?w=500&h=500&fit=crop',\n        category: 'Home & Garden',\n        stock: 55,\n        rating: 4.5,\n        reviews: 203\n    },\n    {\n        id: '15',\n        name: 'Candle Set',\n        price: 24.99,\n        description: 'Set of 3 scented candles with relaxing lavender, vanilla, and eucalyptus.',\n        image: 'https://images.unsplash.com/photo-1543946602-a0fce8117697?w=500&h=500&fit=crop',\n        category: 'Home & Garden',\n        stock: 120,\n        rating: 4.6,\n        reviews: 289\n    },\n    {\n        id: '16',\n        name: 'Yoga Mat',\n        price: 29.99,\n        description: 'Non-slip yoga mat with extra cushioning for comfortable workouts.',\n        image: 'https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?w=500&h=500&fit=crop',\n        category: 'Sports',\n        stock: 40,\n        rating: 4.6,\n        reviews: 134\n    },\n    {\n        id: '17',\n        name: 'Water Bottle',\n        price: 16.99,\n        description: 'Stainless steel water bottle with insulation to keep drinks cold or hot.',\n        image: 'https://images.unsplash.com/photo-1602143407151-7111542de6e8?w=500&h=500&fit=crop',\n        category: 'Sports',\n        stock: 80,\n        rating: 4.4,\n        reviews: 203\n    },\n    {\n        id: '18',\n        name: 'Resistance Bands',\n        price: 22.99,\n        description: 'Set of 5 resistance bands with different strength levels for home workouts.',\n        image: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=500&h=500&fit=crop',\n        category: 'Sports',\n        stock: 65,\n        rating: 4.5,\n        reviews: 178\n    },\n    {\n        id: '19',\n        name: 'Dumbbell Set',\n        price: 89.99,\n        description: 'Adjustable dumbbell set with multiple weight plates for strength training.',\n        image: 'https://images.unsplash.com/photo-1517836357463-d25dfeac3438?w=500&h=500&fit=crop',\n        category: 'Sports',\n        stock: 20,\n        rating: 4.8,\n        reviews: 145\n    },\n    {\n        id: '20',\n        name: 'Running Shoes',\n        price: 119.99,\n        description: 'Professional running shoes with advanced cushioning and breathable design.',\n        image: 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=500&h=500&fit=crop',\n        category: 'Sports',\n        stock: 35,\n        rating: 4.7,\n        reviews: 267\n    }\n];\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/data/products.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"2cd3381a871b\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGRlZXBzXFxPbmVEcml2ZVxcRGVza3RvcFxcTmV3IGZvbGRlciAoMTEpXFxlY29tbWVyY2Utc3RvcmVcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjJjZDMzODFhODcxYlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ })

});