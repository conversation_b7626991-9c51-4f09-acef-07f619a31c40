export interface RetryOptions {
  maxRetries?: number;
  delay?: number;
  backoff?: boolean;
}

export const sleep = (ms: number): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

export const retryAsync = async <T>(
  fn: () => Promise<T>,
  options: RetryOptions = {}
): Promise<T> => {
  const { maxRetries = 3, delay = 1000, backoff = true } = options;
  
  let lastError: Error;
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error as Error;
      
      if (attempt === maxRetries) {
        throw lastError;
      }
      
      const waitTime = backoff ? delay * Math.pow(2, attempt) : delay;
      console.warn(`Attempt ${attempt + 1} failed, retrying in ${waitTime}ms...`, error);
      await sleep(waitTime);
    }
  }
  
  throw lastError!;
};

export const isNetworkError = (error: any): boolean => {
  return (
    error instanceof TypeError ||
    error.message?.includes('fetch') ||
    error.message?.includes('network') ||
    error.code === 'NETWORK_ERROR'
  );
};

export const retryWithNetworkCheck = async <T>(
  fn: () => Promise<T>,
  options: RetryOptions = {}
): Promise<T> => {
  return retryAsync(async () => {
    if (!navigator.onLine) {
      throw new Error('No internet connection');
    }
    return await fn();
  }, options);
};
