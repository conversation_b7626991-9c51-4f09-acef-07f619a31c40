{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%2811%29/ecommerce-store/src/app/loading.tsx"], "sourcesContent": ["import React from 'react';\n// import { ShoppingBag, Loader2 } from 'lucide-react';\n\nexport default function Loading() {\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-purple-50\">\n      <div className=\"text-center max-w-md mx-auto px-6\">\n        {/* Animated Logo */}\n        <div className=\"relative mb-8\">\n          <div className=\"inline-flex items-center justify-center w-20 h-20 bg-white rounded-full shadow-lg animate-bounce\">\n            <span className=\"text-4xl text-blue-600 animate-pulse\">🛒</span>\n          </div>\n          <div className=\"absolute -top-1 -right-1 w-6 h-6 bg-blue-600 rounded-full animate-ping\"></div>\n        </div>\n\n        {/* Loading Spinner */}\n        <div className=\"flex items-center justify-center mb-6\">\n          <div className=\"h-8 w-8 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin mr-3\"></div>\n          <div className=\"flex space-x-1\">\n            <div className=\"w-2 h-2 bg-blue-600 rounded-full animate-bounce\" style={{ animationDelay: '0ms' }}></div>\n            <div className=\"w-2 h-2 bg-blue-600 rounded-full animate-bounce\" style={{ animationDelay: '150ms' }}></div>\n            <div className=\"w-2 h-2 bg-blue-600 rounded-full animate-bounce\" style={{ animationDelay: '300ms' }}></div>\n          </div>\n        </div>\n\n        {/* Loading Text */}\n        <div className=\"space-y-2\">\n          <h2 className=\"text-2xl font-bold text-gray-800 animate-pulse\">ShopEasy</h2>\n          <p className=\"text-gray-600 animate-fade-in\">Loading your shopping experience...</p>\n        </div>\n\n        {/* Progress Bar */}\n        <div className=\"mt-8 w-full bg-gray-200 rounded-full h-2 overflow-hidden\">\n          <div className=\"h-full bg-gradient-to-r from-blue-600 to-purple-600 rounded-full animate-pulse\"></div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAGe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAK,WAAU;0CAAuC;;;;;;;;;;;sCAEzD,8OAAC;4BAAI,WAAU;;;;;;;;;;;;8BAIjB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;oCAAkD,OAAO;wCAAE,gBAAgB;oCAAM;;;;;;8CAChG,8OAAC;oCAAI,WAAU;oCAAkD,OAAO;wCAAE,gBAAgB;oCAAQ;;;;;;8CAClG,8OAAC;oCAAI,WAAU;oCAAkD,OAAO;wCAAE,gBAAgB;oCAAQ;;;;;;;;;;;;;;;;;;8BAKtG,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAiD;;;;;;sCAC/D,8OAAC;4BAAE,WAAU;sCAAgC;;;;;;;;;;;;8BAI/C,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;AAKzB", "debugId": null}}]}