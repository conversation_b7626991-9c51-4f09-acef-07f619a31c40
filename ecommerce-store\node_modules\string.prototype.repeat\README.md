# ES6 `String.prototype.repeat` polyfill [![Build status](https://travis-ci.org/mathiasbynens/String.prototype.repeat.svg?branch=master)](https://travis-ci.org/mathiasbynens/String.prototype.repeat)

A robust & optimized polyfill for [the `String.prototype.repeat` method in ECMAScript 6](http://ecma-international.org/ecma-262/6.0/#sec-string.prototype.repeat).

This package implements the [es-shim API](https://github.com/es-shims/api) interface. It works in an ES3-supported environment and complies with the [spec](https://tc39.es/ecma262/#sec-string.prototype.repeat).

Other polyfills for `String.prototype.repeat` are available:

* <https://github.com/paulmillr/es6-shim/blob/d8c4ec246a15e7df55da60b7f9b745af84ca9021/es6-shim.js#L146-L154> by [<PERSON>](http://paulmillr.com/) (~~[fails 8 tests](https://github.com/paulmillr/es6-shim/issues/164)~~ now passes all tests)

## Installation

Via [npm](https://www.npmjs.com/):

```bash
npm install string.prototype.repeat
```

Then, in [Node.js](https://nodejs.org/):

```js
var repeat = require('string.prototype.repeat');
```

In a browser:

```html
<script src="https://bundle.run/string.prototype.repeat"></script>
```

> **NOTE**: It's recommended that you install this module using a package manager
> such as `npm`, because loading multiple polyfills from a CDN (such as `bundle.run`)
> will lead to duplicated code.

## Author

| [![twitter/mathias](https://gravatar.com/avatar/24e08a9ea84deb17ae121074d0f17125?s=70)](https://twitter.com/mathias "Follow @mathias on Twitter") |
|---|
| [Mathias Bynens](https://mathiasbynens.be/) |

## License

This polyfill is available under the [MIT](https://mths.be/mit) license.
