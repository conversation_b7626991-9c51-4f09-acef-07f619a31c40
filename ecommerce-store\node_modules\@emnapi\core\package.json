{"name": "@emnapi/core", "version": "1.4.4", "description": "emnapi core", "main": "index.js", "module": "./dist/emnapi-core.esm-bundler.js", "types": "./dist/emnapi-core.d.ts", "sideEffects": false, "exports": {".": {"types": {"module": "./dist/emnapi-core.d.ts", "import": "./dist/emnapi-core.d.mts", "default": "./dist/emnapi-core.d.ts"}, "module": "./dist/emnapi-core.esm-bundler.js", "import": "./dist/emnapi-core.mjs", "default": "./index.js"}, "./dist/emnapi-core.cjs.min": {"types": "./dist/emnapi-core.d.ts", "default": "./dist/emnapi-core.cjs.min.js"}, "./dist/emnapi-core.min.mjs": {"types": "./dist/emnapi-core.d.mts", "default": "./dist/emnapi-core.min.mjs"}}, "dependencies": {"@emnapi/wasi-threads": "1.0.3", "tslib": "^2.4.0"}, "scripts": {"build": "node ./script/build.js"}, "repository": {"type": "git", "url": "git+https://github.com/toyobayashi/emnapi.git"}, "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/toyobayashi/emnapi/issues"}, "homepage": "https://github.com/toyobayashi/emnapi#readme", "publishConfig": {"access": "public"}}