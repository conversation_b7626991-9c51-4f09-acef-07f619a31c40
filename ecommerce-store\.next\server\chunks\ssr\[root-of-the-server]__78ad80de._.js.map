{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%2811%29/ecommerce-store/src/app/context/CartContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useReducer, ReactNode } from 'react';\nimport { CartItem, Product, CartContextType } from '../types';\n\ntype CartAction =\n  | { type: 'ADD_TO_CART'; product: Product; quantity: number }\n  | { type: 'REMOVE_FROM_CART'; productId: string }\n  | { type: 'UPDATE_QUANTITY'; productId: string; quantity: number }\n  | { type: 'CLEAR_CART' };\n\ninterface CartState {\n  items: CartItem[];\n}\n\nconst cartReducer = (state: CartState, action: CartAction): CartState => {\n  switch (action.type) {\n    case 'ADD_TO_CART': {\n      const existingItem = state.items.find(item => item.product.id === action.product.id);\n      \n      if (existingItem) {\n        return {\n          ...state,\n          items: state.items.map(item =>\n            item.product.id === action.product.id\n              ? { ...item, quantity: item.quantity + action.quantity }\n              : item\n          ),\n        };\n      }\n      \n      return {\n        ...state,\n        items: [...state.items, { product: action.product, quantity: action.quantity }],\n      };\n    }\n    \n    case 'REMOVE_FROM_CART':\n      return {\n        ...state,\n        items: state.items.filter(item => item.product.id !== action.productId),\n      };\n    \n    case 'UPDATE_QUANTITY':\n      if (action.quantity <= 0) {\n        return {\n          ...state,\n          items: state.items.filter(item => item.product.id !== action.productId),\n        };\n      }\n      \n      return {\n        ...state,\n        items: state.items.map(item =>\n          item.product.id === action.productId\n            ? { ...item, quantity: action.quantity }\n            : item\n        ),\n      };\n    \n    case 'CLEAR_CART':\n      return { ...state, items: [] };\n    \n    default:\n      return state;\n  }\n};\n\nconst CartContext = createContext<CartContextType | undefined>(undefined);\n\nexport const CartProvider: React.FC<{ children: ReactNode }> = ({ children }) => {\n  const [state, dispatch] = useReducer(cartReducer, { items: [] });\n\n  const addToCart = (product: Product, quantity: number = 1) => {\n    dispatch({ type: 'ADD_TO_CART', product, quantity });\n  };\n\n  const removeFromCart = (productId: string) => {\n    dispatch({ type: 'REMOVE_FROM_CART', productId });\n  };\n\n  const updateQuantity = (productId: string, quantity: number) => {\n    dispatch({ type: 'UPDATE_QUANTITY', productId, quantity });\n  };\n\n  const clearCart = () => {\n    dispatch({ type: 'CLEAR_CART' });\n  };\n\n  const getTotalPrice = () => {\n    return state.items.reduce((total, item) => total + item.product.price * item.quantity, 0);\n  };\n\n  const getTotalItems = () => {\n    return state.items.reduce((total, item) => total + item.quantity, 0);\n  };\n\n  const value: CartContextType = {\n    items: state.items,\n    addToCart,\n    removeFromCart,\n    updateQuantity,\n    clearCart,\n    getTotalPrice,\n    getTotalItems,\n  };\n\n  return <CartContext.Provider value={value}>{children}</CartContext.Provider>;\n};\n\nexport const useCart = (): CartContextType => {\n  const context = useContext(CartContext);\n  if (context === undefined) {\n    throw new Error('useCart must be used within a CartProvider');\n  }\n  return context;\n};\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAeA,MAAM,cAAc,CAAC,OAAkB;IACrC,OAAQ,OAAO,IAAI;QACjB,KAAK;YAAe;gBAClB,MAAM,eAAe,MAAM,KAAK,CAAC,IAAI,CAAC,CAAA,OAAQ,KAAK,OAAO,CAAC,EAAE,KAAK,OAAO,OAAO,CAAC,EAAE;gBAEnF,IAAI,cAAc;oBAChB,OAAO;wBACL,GAAG,KAAK;wBACR,OAAO,MAAM,KAAK,CAAC,GAAG,CAAC,CAAA,OACrB,KAAK,OAAO,CAAC,EAAE,KAAK,OAAO,OAAO,CAAC,EAAE,GACjC;gCAAE,GAAG,IAAI;gCAAE,UAAU,KAAK,QAAQ,GAAG,OAAO,QAAQ;4BAAC,IACrD;oBAER;gBACF;gBAEA,OAAO;oBACL,GAAG,KAAK;oBACR,OAAO;2BAAI,MAAM,KAAK;wBAAE;4BAAE,SAAS,OAAO,OAAO;4BAAE,UAAU,OAAO,QAAQ;wBAAC;qBAAE;gBACjF;YACF;QAEA,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,OAAO,MAAM,KAAK,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,OAAO,CAAC,EAAE,KAAK,OAAO,SAAS;YACxE;QAEF,KAAK;YACH,IAAI,OAAO,QAAQ,IAAI,GAAG;gBACxB,OAAO;oBACL,GAAG,KAAK;oBACR,OAAO,MAAM,KAAK,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,OAAO,CAAC,EAAE,KAAK,OAAO,SAAS;gBACxE;YACF;YAEA,OAAO;gBACL,GAAG,KAAK;gBACR,OAAO,MAAM,KAAK,CAAC,GAAG,CAAC,CAAA,OACrB,KAAK,OAAO,CAAC,EAAE,KAAK,OAAO,SAAS,GAChC;wBAAE,GAAG,IAAI;wBAAE,UAAU,OAAO,QAAQ;oBAAC,IACrC;YAER;QAEF,KAAK;YACH,OAAO;gBAAE,GAAG,KAAK;gBAAE,OAAO,EAAE;YAAC;QAE/B;YACE,OAAO;IACX;AACF;AAEA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,MAAM,eAAkD,CAAC,EAAE,QAAQ,EAAE;IAC1E,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,aAAa;QAAE,OAAO,EAAE;IAAC;IAE9D,MAAM,YAAY,CAAC,SAAkB,WAAmB,CAAC;QACvD,SAAS;YAAE,MAAM;YAAe;YAAS;QAAS;IACpD;IAEA,MAAM,iBAAiB,CAAC;QACtB,SAAS;YAAE,MAAM;YAAoB;QAAU;IACjD;IAEA,MAAM,iBAAiB,CAAC,WAAmB;QACzC,SAAS;YAAE,MAAM;YAAmB;YAAW;QAAS;IAC1D;IAEA,MAAM,YAAY;QAChB,SAAS;YAAE,MAAM;QAAa;IAChC;IAEA,MAAM,gBAAgB;QACpB,OAAO,MAAM,KAAK,CAAC,MAAM,CAAC,CAAC,OAAO,OAAS,QAAQ,KAAK,OAAO,CAAC,KAAK,GAAG,KAAK,QAAQ,EAAE;IACzF;IAEA,MAAM,gBAAgB;QACpB,OAAO,MAAM,KAAK,CAAC,MAAM,CAAC,CAAC,OAAO,OAAS,QAAQ,KAAK,QAAQ,EAAE;IACpE;IAEA,MAAM,QAAyB;QAC7B,OAAO,MAAM,KAAK;QAClB;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBAAO,8OAAC,YAAY,QAAQ;QAAC,OAAO;kBAAQ;;;;;;AAC9C;AAEO,MAAM,UAAU;IACrB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 144, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%2811%29/ecommerce-store/src/app/components/Toast.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useEffect, useState } from 'react';\n// import { CheckCircle, X, ShoppingCart } from 'lucide-react';\n\ninterface ToastProps {\n  message: string;\n  type?: 'success' | 'error' | 'info';\n  duration?: number;\n  onClose?: () => void;\n}\n\nconst Toast: React.FC<ToastProps> = ({ \n  message, \n  type = 'success', \n  duration = 3000, \n  onClose \n}) => {\n  const [isVisible, setIsVisible] = useState(false);\n\n  useEffect(() => {\n    // Show toast\n    setIsVisible(true);\n\n    // Auto hide after duration\n    const timer = setTimeout(() => {\n      setIsVisible(false);\n      if (onClose) {\n        setTimeout(onClose, 300); // Wait for fade out animation\n      }\n    }, duration);\n\n    return () => clearTimeout(timer);\n  }, [duration, onClose]);\n\n  const getIcon = () => {\n    switch (type) {\n      case 'success':\n        return <span className=\"text-green-500 text-lg\">✅</span>;\n      case 'error':\n        return <span className=\"text-red-500 text-lg\">❌</span>;\n      case 'info':\n        return <span className=\"text-blue-500 text-lg\">🛒</span>;\n      default:\n        return <span className=\"text-green-500 text-lg\">✅</span>;\n    }\n  };\n\n  const getBgColor = () => {\n    switch (type) {\n      case 'success':\n        return 'bg-green-50 border-green-200';\n      case 'error':\n        return 'bg-red-50 border-red-200';\n      case 'info':\n        return 'bg-blue-50 border-blue-200';\n      default:\n        return 'bg-green-50 border-green-200';\n    }\n  };\n\n  return (\n    <div\n      className={`fixed top-4 right-4 z-50 max-w-sm w-full transform transition-all duration-300 ${\n        isVisible \n          ? 'translate-x-0 opacity-100 scale-100' \n          : 'translate-x-full opacity-0 scale-95'\n      }`}\n    >\n      <div className={`${getBgColor()} border rounded-lg shadow-lg p-4 flex items-center space-x-3`}>\n        <div className=\"flex-shrink-0\">\n          {getIcon()}\n        </div>\n        <div className=\"flex-1\">\n          <p className=\"text-sm font-medium text-gray-800\">{message}</p>\n        </div>\n        {onClose && (\n          <button\n            type=\"button\"\n            onClick={onClose}\n            className=\"flex-shrink-0 text-gray-400 hover:text-gray-600 transition-colors\"\n            aria-label=\"Close notification\"\n          >\n            <span className=\"text-gray-400\">✕</span>\n          </button>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default Toast;\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAYA,MAAM,QAA8B,CAAC,EACnC,OAAO,EACP,OAAO,SAAS,EAChB,WAAW,IAAI,EACf,OAAO,EACR;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,aAAa;QACb,aAAa;QAEb,2BAA2B;QAC3B,MAAM,QAAQ,WAAW;YACvB,aAAa;YACb,IAAI,SAAS;gBACX,WAAW,SAAS,MAAM,8BAA8B;YAC1D;QACF,GAAG;QAEH,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;QAAU;KAAQ;IAEtB,MAAM,UAAU;QACd,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC;oBAAK,WAAU;8BAAyB;;;;;;YAClD,KAAK;gBACH,qBAAO,8OAAC;oBAAK,WAAU;8BAAuB;;;;;;YAChD,KAAK;gBACH,qBAAO,8OAAC;oBAAK,WAAU;8BAAwB;;;;;;YACjD;gBACE,qBAAO,8OAAC;oBAAK,WAAU;8BAAyB;;;;;;QACpD;IACF;IAEA,MAAM,aAAa;QACjB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC;QACC,WAAW,CAAC,+EAA+E,EACzF,YACI,wCACA,uCACJ;kBAEF,cAAA,8OAAC;YAAI,WAAW,GAAG,aAAa,4DAA4D,CAAC;;8BAC3F,8OAAC;oBAAI,WAAU;8BACZ;;;;;;8BAEH,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;kCAAqC;;;;;;;;;;;gBAEnD,yBACC,8OAAC;oBACC,MAAK;oBACL,SAAS;oBACT,WAAU;oBACV,cAAW;8BAEX,cAAA,8OAAC;wBAAK,WAAU;kCAAgB;;;;;;;;;;;;;;;;;;;;;;AAM5C;uCAEe", "debugId": null}}, {"offset": {"line": 286, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%2811%29/ecommerce-store/src/app/context/ToastContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useState, ReactNode } from 'react';\nimport Toast from '../components/Toast';\n\ninterface ToastData {\n  id: string;\n  message: string;\n  type?: 'success' | 'error' | 'info';\n  duration?: number;\n}\n\ninterface ToastContextType {\n  showToast: (message: string, type?: 'success' | 'error' | 'info', duration?: number) => void;\n}\n\nconst ToastContext = createContext<ToastContextType | undefined>(undefined);\n\nexport const ToastProvider: React.FC<{ children: ReactNode }> = ({ children }) => {\n  const [toasts, setToasts] = useState<ToastData[]>([]);\n\n  const showToast = (message: string, type: 'success' | 'error' | 'info' = 'success', duration = 3000) => {\n    const id = Date.now().toString();\n    const newToast: ToastData = { id, message, type, duration };\n    \n    setToasts(prev => [...prev, newToast]);\n  };\n\n  const removeToast = (id: string) => {\n    setToasts(prev => prev.filter(toast => toast.id !== id));\n  };\n\n  return (\n    <ToastContext.Provider value={{ showToast }}>\n      {children}\n      <div className=\"fixed top-4 right-4 z-50 space-y-2\">\n        {toasts.map((toast) => (\n          <Toast\n            key={toast.id}\n            message={toast.message}\n            type={toast.type}\n            duration={toast.duration}\n            onClose={() => removeToast(toast.id)}\n          />\n        ))}\n      </div>\n    </ToastContext.Provider>\n  );\n};\n\nexport const useToast = (): ToastContextType => {\n  const context = useContext(ToastContext);\n  if (context === undefined) {\n    throw new Error('useToast must be used within a ToastProvider');\n  }\n  return context;\n};\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAgBA,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAgC;AAE1D,MAAM,gBAAmD,CAAC,EAAE,QAAQ,EAAE;IAC3E,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,EAAE;IAEpD,MAAM,YAAY,CAAC,SAAiB,OAAqC,SAAS,EAAE,WAAW,IAAI;QACjG,MAAM,KAAK,KAAK,GAAG,GAAG,QAAQ;QAC9B,MAAM,WAAsB;YAAE;YAAI;YAAS;YAAM;QAAS;QAE1D,UAAU,CAAA,OAAQ;mBAAI;gBAAM;aAAS;IACvC;IAEA,MAAM,cAAc,CAAC;QACnB,UAAU,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;IACtD;IAEA,qBACE,8OAAC,aAAa,QAAQ;QAAC,OAAO;YAAE;QAAU;;YACvC;0BACD,8OAAC;gBAAI,WAAU;0BACZ,OAAO,GAAG,CAAC,CAAC,sBACX,8OAAC,kIAAA,CAAA,UAAK;wBAEJ,SAAS,MAAM,OAAO;wBACtB,MAAM,MAAM,IAAI;wBAChB,UAAU,MAAM,QAAQ;wBACxB,SAAS,IAAM,YAAY,MAAM,EAAE;uBAJ9B,MAAM,EAAE;;;;;;;;;;;;;;;;AAUzB;AAEO,MAAM,WAAW;IACtB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 383, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%2811%29/ecommerce-store/src/app/data/products.ts"], "sourcesContent": ["import { Product, Category } from '../types';\n\nexport const categories: Category[] = [\n  { id: '1', name: 'Electronics', slug: 'electronics' },\n  { id: '2', name: 'Clothing', slug: 'clothing' },\n  { id: '3', name: 'Books', slug: 'books' },\n  { id: '4', name: 'Home & Garden', slug: 'home-garden' },\n  { id: '5', name: 'Sports', slug: 'sports' },\n];\n\nexport const products: Product[] = [\n  {\n    id: '1',\n    name: 'Wireless Bluetooth Headphones',\n    price: 79.99,\n    description: 'High-quality wireless headphones with noise cancellation and 30-hour battery life.',\n    image: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=500&h=500&fit=crop',\n    category: 'Electronics',\n    stock: 25,\n    rating: 4.5,\n    reviews: 128,\n  },\n  {\n    id: '2',\n    name: 'Smartphone Case',\n    price: 24.99,\n    description: 'Durable protective case for smartphones with shock absorption and wireless charging support.',\n    image: 'https://images.unsplash.com/photo-1556656793-08538906a9f8?w=500&h=500&fit=crop',\n    category: 'Electronics',\n    stock: 50,\n    rating: 4.2,\n    reviews: 89,\n  },\n  {\n    id: '3',\n    name: 'Laptop Stand',\n    price: 45.99,\n    description: 'Adjustable aluminum laptop stand with cooling design and ergonomic positioning.',\n    image: 'https://images.unsplash.com/photo-1527864550417-7fd91fc51a46?w=500&h=500&fit=crop',\n    category: 'Electronics',\n    stock: 35,\n    rating: 4.6,\n    reviews: 156,\n  },\n  {\n    id: '4',\n    name: 'Wireless Mouse',\n    price: 29.99,\n    description: 'Ergonomic wireless mouse with precision tracking and long battery life.',\n    image: 'https://images.unsplash.com/photo-1527814050087-3793815479db?w=500&h=500&fit=crop',\n    category: 'Electronics',\n    stock: 60,\n    rating: 4.3,\n    reviews: 201,\n  },\n  {\n    id: '5',\n    name: 'Cotton T-Shirt',\n    price: 19.99,\n    description: 'Comfortable 100% cotton t-shirt available in multiple colors and sizes.',\n    image: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=500&h=500&fit=crop',\n    category: 'Clothing',\n    stock: 100,\n    rating: 4.3,\n    reviews: 245,\n  },\n  {\n    id: '6',\n    name: 'Denim Jeans',\n    price: 59.99,\n    description: 'Classic fit denim jeans made from premium quality fabric.',\n    image: 'https://images.unsplash.com/photo-1542272604-787c3835535d?w=500&h=500&fit=crop',\n    category: 'Clothing',\n    stock: 75,\n    rating: 4.4,\n    reviews: 156,\n  },\n  {\n    id: '7',\n    name: 'Hoodie',\n    price: 49.99,\n    description: 'Cozy fleece hoodie with kangaroo pocket and adjustable drawstring.',\n    image: 'https://images.unsplash.com/photo-1556821840-3a63f95609a7?w=500&h=500&fit=crop',\n    category: 'Clothing',\n    stock: 45,\n    rating: 4.5,\n    reviews: 189,\n  },\n  {\n    id: '8',\n    name: 'Sneakers',\n    price: 89.99,\n    description: 'Comfortable running sneakers with breathable mesh and cushioned sole.',\n    image: 'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=500&h=500&fit=crop',\n    category: 'Clothing',\n    stock: 30,\n    rating: 4.7,\n    reviews: 312,\n  },\n  {\n    id: '9',\n    name: 'Programming Book',\n    price: 39.99,\n    description: 'Comprehensive guide to modern web development with practical examples.',\n    image: 'https://images.unsplash.com/photo-1544716278-ca5e3f4abd8c?w=500&h=500&fit=crop',\n    category: 'Books',\n    stock: 30,\n    rating: 4.7,\n    reviews: 92,\n  },\n  {\n    id: '10',\n    name: 'Design Thinking Book',\n    price: 34.99,\n    description: 'Learn the principles of design thinking and user experience design.',\n    image: 'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=500&h=500&fit=crop',\n    category: 'Books',\n    stock: 25,\n    rating: 4.4,\n    reviews: 78,\n  },\n  {\n    id: '11',\n    name: 'Business Strategy Book',\n    price: 42.99,\n    description: 'Essential strategies for modern business leadership and growth.',\n    image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=500&h=500&fit=crop',\n    category: 'Books',\n    stock: 40,\n    rating: 4.2,\n    reviews: 134,\n  },\n  {\n    id: '12',\n    name: 'Coffee Mug',\n    price: 12.99,\n    description: 'Ceramic coffee mug with ergonomic handle and heat retention design.',\n    image: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=500&h=500&fit=crop',\n    category: 'Home & Garden',\n    stock: 200,\n    rating: 4.1,\n    reviews: 67,\n  },\n  {\n    id: '13',\n    name: 'Plant Pot',\n    price: 18.99,\n    description: 'Modern ceramic plant pot with drainage system, perfect for indoor plants.',\n    image: 'https://images.unsplash.com/photo-1485955900006-10f4d324d411?w=500&h=500&fit=crop',\n    category: 'Home & Garden',\n    stock: 85,\n    rating: 4.3,\n    reviews: 156,\n  },\n  {\n    id: '14',\n    name: 'Desk Lamp',\n    price: 35.99,\n    description: 'LED desk lamp with adjustable brightness and USB charging port.',\n    image: 'https://images.unsplash.com/photo-1507473885765-e6ed057f782c?w=500&h=500&fit=crop',\n    category: 'Home & Garden',\n    stock: 55,\n    rating: 4.5,\n    reviews: 203,\n  },\n  {\n    id: '15',\n    name: 'Candle Set',\n    price: 24.99,\n    description: 'Set of 3 scented candles with relaxing lavender, vanilla, and eucalyptus.',\n    image: 'https://images.unsplash.com/photo-1543946602-a0fce8117697?w=500&h=500&fit=crop',\n    category: 'Home & Garden',\n    stock: 120,\n    rating: 4.6,\n    reviews: 289,\n  },\n  {\n    id: '16',\n    name: 'Yoga Mat',\n    price: 29.99,\n    description: 'Non-slip yoga mat with extra cushioning for comfortable workouts.',\n    image: 'https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?w=500&h=500&fit=crop',\n    category: 'Sports',\n    stock: 40,\n    rating: 4.6,\n    reviews: 134,\n  },\n  {\n    id: '17',\n    name: 'Water Bottle',\n    price: 16.99,\n    description: 'Stainless steel water bottle with insulation to keep drinks cold or hot.',\n    image: 'https://images.unsplash.com/photo-1602143407151-7111542de6e8?w=500&h=500&fit=crop',\n    category: 'Sports',\n    stock: 80,\n    rating: 4.4,\n    reviews: 203,\n  },\n  {\n    id: '18',\n    name: 'Resistance Bands',\n    price: 22.99,\n    description: 'Set of 5 resistance bands with different strength levels for home workouts.',\n    image: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=500&h=500&fit=crop',\n    category: 'Sports',\n    stock: 65,\n    rating: 4.5,\n    reviews: 178,\n  },\n  {\n    id: '19',\n    name: 'Dumbbell Set',\n    price: 89.99,\n    description: 'Adjustable dumbbell set with multiple weight plates for strength training.',\n    image: 'https://images.unsplash.com/photo-1517836357463-d25dfeac3438?w=500&h=500&fit=crop',\n    category: 'Sports',\n    stock: 20,\n    rating: 4.8,\n    reviews: 145,\n  },\n  {\n    id: '20',\n    name: 'Running Shoes',\n    price: 119.99,\n    description: 'Professional running shoes with advanced cushioning and breathable design.',\n    image: 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=500&h=500&fit=crop',\n    category: 'Sports',\n    stock: 35,\n    rating: 4.7,\n    reviews: 267,\n  },\n];\n"], "names": [], "mappings": ";;;;AAEO,MAAM,aAAyB;IACpC;QAAE,IAAI;QAAK,MAAM;QAAe,MAAM;IAAc;IACpD;QAAE,IAAI;QAAK,MAAM;QAAY,MAAM;IAAW;IAC9C;QAAE,IAAI;QAAK,MAAM;QAAS,MAAM;IAAQ;IACxC;QAAE,IAAI;QAAK,MAAM;QAAiB,MAAM;IAAc;IACtD;QAAE,IAAI;QAAK,MAAM;QAAU,MAAM;IAAS;CAC3C;AAEM,MAAM,WAAsB;IACjC;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;QACV,OAAO;QACP,QAAQ;QACR,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;QACV,OAAO;QACP,QAAQ;QACR,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;QACV,OAAO;QACP,QAAQ;QACR,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;QACV,OAAO;QACP,QAAQ;QACR,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;QACV,OAAO;QACP,QAAQ;QACR,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;QACV,OAAO;QACP,QAAQ;QACR,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;QACV,OAAO;QACP,QAAQ;QACR,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;QACV,OAAO;QACP,QAAQ;QACR,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;QACV,OAAO;QACP,QAAQ;QACR,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;QACV,OAAO;QACP,QAAQ;QACR,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;QACV,OAAO;QACP,QAAQ;QACR,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;QACV,OAAO;QACP,QAAQ;QACR,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;QACV,OAAO;QACP,QAAQ;QACR,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;QACV,OAAO;QACP,QAAQ;QACR,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;QACV,OAAO;QACP,QAAQ;QACR,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;QACV,OAAO;QACP,QAAQ;QACR,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;QACV,OAAO;QACP,QAAQ;QACR,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;QACV,OAAO;QACP,QAAQ;QACR,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;QACV,OAAO;QACP,QAAQ;QACR,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;QACV,OAAO;QACP,QAAQ;QACR,SAAS;IACX;CACD", "debugId": null}}, {"offset": {"line": 642, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%2811%29/ecommerce-store/src/app/components/Header.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Link from 'next/link';\nimport { useRouter, usePathname } from 'next/navigation';\n// import { ShoppingCart, Search, Menu } from 'lucide-react';\nimport { useCart } from '../context/CartContext';\nimport { categories } from '../data/products';\n\nconst Header: React.FC = () => {\n  const { getTotalItems } = useCart();\n  const router = useRouter();\n  const pathname = usePathname();\n  const [searchQuery, setSearchQuery] = useState('');\n  const [cartClicked, setCartClicked] = useState(false);\n  const [activeNav, setActiveNav] = useState('');\n  const totalItems = getTotalItems();\n\n  const handleSearch = (e: React.FormEvent) => {\n    e.preventDefault();\n    if (searchQuery.trim()) {\n      router.push(`/search?q=${encodeURIComponent(searchQuery.trim())}`);\n    }\n  };\n\n  const handleCartClick = () => {\n    setCartClicked(true);\n    setTimeout(() => setCartClicked(false), 300);\n    router.push('/cart');\n  };\n\n  const handleNavClick = (path: string) => {\n    setActiveNav(path);\n    setTimeout(() => setActiveNav(''), 300);\n  };\n\n  const isActivePage = (path: string) => {\n    if (path === '/' && pathname === '/') return true;\n    if (path !== '/' && pathname.startsWith(path)) return true;\n    return false;\n  };\n\n  return (\n    <header className=\"bg-white shadow-md sticky top-0 z-50\">\n      <div className=\"container mx-auto px-4\">\n        {/* Top bar */}\n        <div className=\"flex items-center justify-between py-4\">\n          {/* Logo */}\n          <Link\n            href=\"/\"\n            className=\"text-2xl font-bold text-blue-600 hover:text-blue-700 transition-colors duration-200 transform hover:scale-105\"\n            aria-label=\"ShopEasy Home\"\n          >\n            ShopEasy\n          </Link>\n\n          {/* Search bar */}\n          <div className=\"hidden md:flex flex-1 max-w-md mx-8\">\n            <form onSubmit={handleSearch} className=\"relative w-full\">\n              <input\n                type=\"text\"\n                value={searchQuery}\n                onChange={(e) => setSearchQuery(e.target.value)}\n                placeholder=\"Search products...\"\n                className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200\"\n                aria-label=\"Search products\"\n              />\n              <button\n                type=\"submit\"\n                className=\"absolute right-3 top-2.5 text-gray-400 hover:text-blue-600 transition-colors\"\n                aria-label=\"Search\"\n              >\n                <span className=\"text-lg\">🔍</span>\n              </button>\n            </form>\n          </div>\n\n          {/* Cart and Menu */}\n          <div className=\"flex items-center space-x-4\">\n            <button\n              type=\"button\"\n              onClick={handleCartClick}\n              className={`relative p-2 text-gray-600 hover:text-blue-600 transition-all duration-200 transform hover:scale-110 rounded-lg hover:bg-blue-50 ${\n                cartClicked ? 'animate-bounce scale-125' : ''\n              }`}\n              aria-label={`Shopping cart with ${totalItems} items`}\n            >\n              <span className={`text-2xl transition-all duration-300 ${cartClicked ? 'text-blue-600' : ''}`}>🛒</span>\n              {totalItems > 0 && (\n                <span className={`absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-medium transition-all duration-300 ${\n                  cartClicked ? 'animate-ping' : 'animate-pulse'\n                }`}>\n                  {totalItems > 99 ? '99+' : totalItems}\n                </span>\n              )}\n            </button>\n            <button\n              type=\"button\"\n              className=\"md:hidden p-2 text-gray-600 hover:text-blue-600 transition-all duration-200 transform hover:scale-110 rounded-lg hover:bg-blue-50\"\n              aria-label=\"Open mobile menu\"\n            >\n              <span className=\"text-2xl\">☰</span>\n            </button>\n          </div>\n        </div>\n\n        {/* Navigation */}\n        <nav className=\"hidden md:block border-t border-gray-200\" role=\"navigation\" aria-label=\"Main navigation\">\n          <ul className=\"flex space-x-8 py-4\">\n            <li>\n              <Link\n                href=\"/\"\n                onClick={() => handleNavClick('/')}\n                className={`text-gray-600 hover:text-blue-600 transition-all duration-300 font-medium relative group px-2 py-1 rounded-md hover:bg-blue-50 transform ${\n                  activeNav === '/' ? 'scale-110 text-blue-600' : ''\n                } ${isActivePage('/') ? 'text-blue-600' : ''}`}\n              >\n                All Products\n                <span className={`absolute bottom-0 left-0 h-0.5 bg-blue-600 transition-all duration-300 ${\n                  isActivePage('/') ? 'w-full' : 'w-0 group-hover:w-full'\n                }`}></span>\n              </Link>\n            </li>\n            {categories.map((category) => {\n              const categoryPath = `/category/${category.slug}`;\n              return (\n                <li key={category.id}>\n                  <Link\n                    href={categoryPath}\n                    onClick={() => handleNavClick(categoryPath)}\n                    className={`text-gray-600 hover:text-blue-600 transition-all duration-300 font-medium relative group px-2 py-1 rounded-md hover:bg-blue-50 transform ${\n                      activeNav === categoryPath ? 'scale-110 text-blue-600' : ''\n                    } ${isActivePage(categoryPath) ? 'text-blue-600' : ''}`}\n                  >\n                    {category.name}\n                    <span className={`absolute bottom-0 left-0 h-0.5 bg-blue-600 transition-all duration-300 ${\n                      isActivePage(categoryPath) ? 'w-full' : 'w-0 group-hover:w-full'\n                    }`}></span>\n                  </Link>\n                </li>\n              );\n            })}\n          </ul>\n        </nav>\n\n        {/* Mobile search */}\n        <div className=\"md:hidden pb-4\">\n          <form onSubmit={handleSearch} className=\"relative\">\n            <input\n              type=\"text\"\n              value={searchQuery}\n              onChange={(e) => setSearchQuery(e.target.value)}\n              placeholder=\"Search products...\"\n              className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200\"\n              aria-label=\"Search products on mobile\"\n            />\n            <button\n              type=\"submit\"\n              className=\"absolute right-3 top-2.5 text-gray-400 hover:text-blue-600 transition-colors\"\n              aria-label=\"Search\"\n            >\n              <span className=\"text-lg\">🔍</span>\n            </button>\n          </form>\n        </div>\n      </div>\n    </header>\n  );\n};\n\nexport default Header;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA,6DAA6D;AAC7D;AACA;AAPA;;;;;;;AASA,MAAM,SAAmB;IACvB,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,UAAO,AAAD;IAChC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,aAAa;IAEnB,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,IAAI,YAAY,IAAI,IAAI;YACtB,OAAO,IAAI,CAAC,CAAC,UAAU,EAAE,mBAAmB,YAAY,IAAI,KAAK;QACnE;IACF;IAEA,MAAM,kBAAkB;QACtB,eAAe;QACf,WAAW,IAAM,eAAe,QAAQ;QACxC,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,iBAAiB,CAAC;QACtB,aAAa;QACb,WAAW,IAAM,aAAa,KAAK;IACrC;IAEA,MAAM,eAAe,CAAC;QACpB,IAAI,SAAS,OAAO,aAAa,KAAK,OAAO;QAC7C,IAAI,SAAS,OAAO,SAAS,UAAU,CAAC,OAAO,OAAO;QACtD,OAAO;IACT;IAEA,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;4BACV,cAAW;sCACZ;;;;;;sCAKD,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAK,UAAU;gCAAc,WAAU;;kDACtC,8OAAC;wCACC,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wCAC9C,aAAY;wCACZ,WAAU;wCACV,cAAW;;;;;;kDAEb,8OAAC;wCACC,MAAK;wCACL,WAAU;wCACV,cAAW;kDAEX,cAAA,8OAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;;;;;;;;;;;;sCAMhC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,SAAS;oCACT,WAAW,CAAC,iIAAiI,EAC3I,cAAc,6BAA6B,IAC3C;oCACF,cAAY,CAAC,mBAAmB,EAAE,WAAW,MAAM,CAAC;;sDAEpD,8OAAC;4CAAK,WAAW,CAAC,qCAAqC,EAAE,cAAc,kBAAkB,IAAI;sDAAE;;;;;;wCAC9F,aAAa,mBACZ,8OAAC;4CAAK,WAAW,CAAC,qJAAqJ,EACrK,cAAc,iBAAiB,iBAC/B;sDACC,aAAa,KAAK,QAAQ;;;;;;;;;;;;8CAIjC,8OAAC;oCACC,MAAK;oCACL,WAAU;oCACV,cAAW;8CAEX,cAAA,8OAAC;wCAAK,WAAU;kDAAW;;;;;;;;;;;;;;;;;;;;;;;8BAMjC,8OAAC;oBAAI,WAAU;oBAA2C,MAAK;oBAAa,cAAW;8BACrF,cAAA,8OAAC;wBAAG,WAAU;;0CACZ,8OAAC;0CACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,SAAS,IAAM,eAAe;oCAC9B,WAAW,CAAC,yIAAyI,EACnJ,cAAc,MAAM,4BAA4B,GACjD,CAAC,EAAE,aAAa,OAAO,kBAAkB,IAAI;;wCAC/C;sDAEC,8OAAC;4CAAK,WAAW,CAAC,uEAAuE,EACvF,aAAa,OAAO,WAAW,0BAC/B;;;;;;;;;;;;;;;;;4BAGL,8HAAA,CAAA,aAAU,CAAC,GAAG,CAAC,CAAC;gCACf,MAAM,eAAe,CAAC,UAAU,EAAE,SAAS,IAAI,EAAE;gCACjD,qBACE,8OAAC;8CACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAM;wCACN,SAAS,IAAM,eAAe;wCAC9B,WAAW,CAAC,yIAAyI,EACnJ,cAAc,eAAe,4BAA4B,GAC1D,CAAC,EAAE,aAAa,gBAAgB,kBAAkB,IAAI;;4CAEtD,SAAS,IAAI;0DACd,8OAAC;gDAAK,WAAW,CAAC,uEAAuE,EACvF,aAAa,gBAAgB,WAAW,0BACxC;;;;;;;;;;;;mCAXG,SAAS,EAAE;;;;;4BAexB;;;;;;;;;;;;8BAKJ,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAK,UAAU;wBAAc,WAAU;;0CACtC,8OAAC;gCACC,MAAK;gCACL,OAAO;gCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gCAC9C,aAAY;gCACZ,WAAU;gCACV,cAAW;;;;;;0CAEb,8OAAC;gCACC,MAAK;gCACL,WAAU;gCACV,cAAW;0CAEX,cAAA,8OAAC;oCAAK,WAAU;8CAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOxC;uCAEe", "debugId": null}}, {"offset": {"line": 947, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%2811%29/ecommerce-store/src/app/components/PageTransition.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useEffect, useState } from 'react';\nimport { usePathname } from 'next/navigation';\n\ninterface PageTransitionProps {\n  children: React.ReactNode;\n}\n\nconst PageTransition: React.FC<PageTransitionProps> = ({ children }) => {\n  const pathname = usePathname();\n  const [isVisible, setIsVisible] = useState(false);\n  const [displayChildren, setDisplayChildren] = useState(children);\n\n  useEffect(() => {\n    // Start fade out\n    setIsVisible(false);\n    \n    // After fade out, update children and fade in\n    const timer = setTimeout(() => {\n      setDisplayChildren(children);\n      setIsVisible(true);\n    }, 150);\n\n    return () => clearTimeout(timer);\n  }, [pathname, children]);\n\n  useEffect(() => {\n    // Initial load\n    setIsVisible(true);\n  }, []);\n\n  return (\n    <div\n      className={`transition-all duration-300 ease-in-out ${\n        isVisible \n          ? 'opacity-100 translate-y-0' \n          : 'opacity-0 translate-y-4'\n      }`}\n    >\n      {displayChildren}\n    </div>\n  );\n};\n\nexport default PageTransition;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AASA,MAAM,iBAAgD,CAAC,EAAE,QAAQ,EAAE;IACjE,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,iBAAiB;QACjB,aAAa;QAEb,8CAA8C;QAC9C,MAAM,QAAQ,WAAW;YACvB,mBAAmB;YACnB,aAAa;QACf,GAAG;QAEH,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;QAAU;KAAS;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,eAAe;QACf,aAAa;IACf,GAAG,EAAE;IAEL,qBACE,8OAAC;QACC,WAAW,CAAC,wCAAwC,EAClD,YACI,8BACA,2BACJ;kBAED;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 994, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%2811%29/ecommerce-store/src/app/components/ErrorBoundary.tsx"], "sourcesContent": ["'use client';\n\nimport React, { Component, ReactNode } from 'react';\n\ninterface Props {\n  children: ReactNode;\n  fallback?: ReactNode;\n}\n\ninterface State {\n  hasError: boolean;\n  error?: Error;\n}\n\nclass ErrorBoundary extends Component<Props, State> {\n  constructor(props: Props) {\n    super(props);\n    this.state = { hasError: false };\n  }\n\n  static getDerivedStateFromError(error: Error): State {\n    return { hasError: true, error };\n  }\n\n  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {\n    console.error('Error caught by boundary:', error, errorInfo);\n  }\n\n  handleRetry = () => {\n    this.setState({ hasError: false, error: undefined });\n  };\n\n  render() {\n    if (this.state.hasError) {\n      if (this.props.fallback) {\n        return this.props.fallback;\n      }\n\n      return (\n        <div className=\"min-h-screen flex items-center justify-center bg-gray-50\">\n          <div className=\"max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center\">\n            <div className=\"text-6xl mb-4\">⚠️</div>\n            <h1 className=\"text-2xl font-bold text-gray-800 mb-2\">Oops! Something went wrong</h1>\n            <p className=\"text-gray-600 mb-6\">\n              We're sorry, but something unexpected happened. Please try refreshing the page.\n            </p>\n            <div className=\"space-y-3\">\n              <button\n                type=\"button\"\n                onClick={this.handleRetry}\n                className=\"w-full bg-blue-600 text-white py-2 px-4 rounded-lg font-medium hover:bg-blue-700 active:bg-blue-800 transition-all duration-200 flex items-center justify-center space-x-2 transform hover:scale-105 active:scale-95\"\n              >\n                <span>🔄 Try Again</span>\n              </button>\n              <button\n                type=\"button\"\n                onClick={() => window.location.href = '/'}\n                className=\"w-full bg-gray-200 text-gray-800 py-2 px-4 rounded-lg font-medium hover:bg-gray-300 active:bg-gray-400 transition-all duration-200 transform hover:scale-105 active:scale-95\"\n              >\n                Go to Homepage\n              </button>\n            </div>\n            {process.env.NODE_ENV === 'development' && this.state.error && (\n              <details className=\"mt-6 text-left\">\n                <summary className=\"cursor-pointer text-sm text-gray-500 hover:text-gray-700\">\n                  Error Details (Development)\n                </summary>\n                <pre className=\"mt-2 text-xs text-red-600 bg-red-50 p-3 rounded overflow-auto\">\n                  {this.state.error.stack}\n                </pre>\n              </details>\n            )}\n          </div>\n        </div>\n      );\n    }\n\n    return this.props.children;\n  }\n}\n\nexport default ErrorBoundary;\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAcA,MAAM,sBAAsB,qMAAA,CAAA,YAAS;IACnC,YAAY,KAAY,CAAE;QACxB,KAAK,CAAC;QACN,IAAI,CAAC,KAAK,GAAG;YAAE,UAAU;QAAM;IACjC;IAEA,OAAO,yBAAyB,KAAY,EAAS;QACnD,OAAO;YAAE,UAAU;YAAM;QAAM;IACjC;IAEA,kBAAkB,KAAY,EAAE,SAA0B,EAAE;QAC1D,QAAQ,KAAK,CAAC,6BAA6B,OAAO;IACpD;IAEA,cAAc;QACZ,IAAI,CAAC,QAAQ,CAAC;YAAE,UAAU;YAAO,OAAO;QAAU;IACpD,EAAE;IAEF,SAAS;QACP,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;YACvB,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;gBACvB,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;YAC5B;YAEA,qBACE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCAAgB;;;;;;sCAC/B,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,8OAAC;4BAAE,WAAU;sCAAqB;;;;;;sCAGlC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,SAAS,IAAI,CAAC,WAAW;oCACzB,WAAU;8CAEV,cAAA,8OAAC;kDAAK;;;;;;;;;;;8CAER,8OAAC;oCACC,MAAK;oCACL,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;oCACtC,WAAU;8CACX;;;;;;;;;;;;wBAIF,oDAAyB,iBAAiB,IAAI,CAAC,KAAK,CAAC,KAAK,kBACzD,8OAAC;4BAAQ,WAAU;;8CACjB,8OAAC;oCAAQ,WAAU;8CAA2D;;;;;;8CAG9E,8OAAC;oCAAI,WAAU;8CACZ,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;QAOrC;QAEA,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;IAC5B;AACF;uCAEe", "debugId": null}}]}