"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/components/Header.tsx":
/*!***************************************!*\
  !*** ./src/app/components/Header.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../../../AppData/Local/npm-cache/_npx/8b377f6eec906bc4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../../../AppData/Local/npm-cache/_npx/8b377f6eec906bc4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/../../../../AppData/Local/npm-cache/_npx/8b377f6eec906bc4/node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/../../../../AppData/Local/npm-cache/_npx/8b377f6eec906bc4/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Menu_Search_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,Search,ShoppingCart!=!lucide-react */ \"(app-pages-browser)/../../../../node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Menu_Search_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,Search,ShoppingCart!=!lucide-react */ \"(app-pages-browser)/../../../../node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _barrel_optimize_names_Menu_Search_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,Search,ShoppingCart!=!lucide-react */ \"(app-pages-browser)/../../../../node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _context_CartContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../context/CartContext */ \"(app-pages-browser)/./src/app/context/CartContext.tsx\");\n/* harmony import */ var _data_products__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../data/products */ \"(app-pages-browser)/./src/app/data/products.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst Header = ()=>{\n    _s();\n    const { getTotalItems } = (0,_context_CartContext__WEBPACK_IMPORTED_MODULE_4__.useCart)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [cartClicked, setCartClicked] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeNav, setActiveNav] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const totalItems = getTotalItems();\n    const handleSearch = (e)=>{\n        e.preventDefault();\n        if (searchQuery.trim()) {\n            router.push(\"/search?q=\".concat(encodeURIComponent(searchQuery.trim())));\n        }\n    };\n    const handleCartClick = ()=>{\n        setCartClicked(true);\n        setTimeout(()=>setCartClicked(false), 300);\n        router.push('/cart');\n    };\n    const handleNavClick = (path)=>{\n        setActiveNav(path);\n        setTimeout(()=>setActiveNav(''), 300);\n    };\n    const isActivePage = (path)=>{\n        if (path === '/' && pathname === '/') return true;\n        if (path !== '/' && pathname.startsWith(path)) return true;\n        return false;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"bg-white shadow-md sticky top-0 z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between py-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/\",\n                            className: \"text-2xl font-bold text-blue-600 hover:text-blue-700 transition-colors duration-200 transform hover:scale-105\",\n                            \"aria-label\": \"ShopEasy Home\",\n                            children: \"ShopEasy\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\New folder (11)\\\\ecommerce-store\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex flex-1 max-w-md mx-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSearch,\n                                className: \"relative w-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: searchQuery,\n                                        onChange: (e)=>setSearchQuery(e.target.value),\n                                        placeholder: \"Search products...\",\n                                        className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200\",\n                                        \"aria-label\": \"Search products\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\New folder (11)\\\\ecommerce-store\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                                        lineNumber: 60,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        className: \"absolute right-3 top-2.5 text-gray-400 hover:text-blue-600 transition-colors\",\n                                        \"aria-label\": \"Search\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Search_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\New folder (11)\\\\ecommerce-store\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                                            lineNumber: 73,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\New folder (11)\\\\ecommerce-store\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                                        lineNumber: 68,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\New folder (11)\\\\ecommerce-store\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                                lineNumber: 59,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\New folder (11)\\\\ecommerce-store\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: handleCartClick,\n                                    className: \"relative p-2 text-gray-600 hover:text-blue-600 transition-all duration-200 transform hover:scale-110 rounded-lg hover:bg-blue-50 \".concat(cartClicked ? 'animate-bounce scale-125' : ''),\n                                    \"aria-label\": \"Shopping cart with \".concat(totalItems, \" items\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Search_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-6 w-6 transition-all duration-300 \".concat(cartClicked ? 'text-blue-600' : '')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\New folder (11)\\\\ecommerce-store\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        totalItems > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-medium transition-all duration-300 \".concat(cartClicked ? 'animate-ping' : 'animate-pulse'),\n                                            children: totalItems > 99 ? '99+' : totalItems\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\New folder (11)\\\\ecommerce-store\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                                            lineNumber: 90,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\New folder (11)\\\\ecommerce-store\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    className: \"md:hidden p-2 text-gray-600 hover:text-blue-600 transition-all duration-200 transform hover:scale-110 rounded-lg hover:bg-blue-50\",\n                                    \"aria-label\": \"Open mobile menu\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Search_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\New folder (11)\\\\ecommerce-store\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\New folder (11)\\\\ecommerce-store\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\New folder (11)\\\\ecommerce-store\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\New folder (11)\\\\ecommerce-store\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"hidden md:block border-t border-gray-200\",\n                    role: \"navigation\",\n                    \"aria-label\": \"Main navigation\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"flex space-x-8 py-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/\",\n                                    onClick: ()=>handleNavClick('/'),\n                                    className: \"text-gray-600 hover:text-blue-600 transition-all duration-300 font-medium relative group px-2 py-1 rounded-md hover:bg-blue-50 transform \".concat(activeNav === '/' ? 'scale-110 text-blue-600' : '', \" \").concat(isActivePage('/') ? 'text-blue-600' : ''),\n                                    children: [\n                                        \"All Products\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"absolute bottom-0 left-0 h-0.5 bg-blue-600 transition-all duration-300 \".concat(isActivePage('/') ? 'w-full' : 'w-0 group-hover:w-full')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\New folder (11)\\\\ecommerce-store\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                                            lineNumber: 119,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\New folder (11)\\\\ecommerce-store\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\New folder (11)\\\\ecommerce-store\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                                lineNumber: 110,\n                                columnNumber: 13\n                            }, undefined),\n                            _data_products__WEBPACK_IMPORTED_MODULE_5__.categories.map((category)=>{\n                                const categoryPath = \"/category/\".concat(category.slug);\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: categoryPath,\n                                        onClick: ()=>handleNavClick(categoryPath),\n                                        className: \"text-gray-600 hover:text-blue-600 transition-all duration-300 font-medium relative group px-2 py-1 rounded-md hover:bg-blue-50 transform \".concat(activeNav === categoryPath ? 'scale-110 text-blue-600' : '', \" \").concat(isActivePage(categoryPath) ? 'text-blue-600' : ''),\n                                        children: [\n                                            category.name,\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"absolute bottom-0 left-0 h-0.5 bg-blue-600 transition-all duration-300 \".concat(isActivePage(categoryPath) ? 'w-full' : 'w-0 group-hover:w-full')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\New folder (11)\\\\ecommerce-store\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                                                lineNumber: 136,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\New folder (11)\\\\ecommerce-store\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, category.id, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\New folder (11)\\\\ecommerce-store\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 17\n                                }, undefined);\n                            })\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\New folder (11)\\\\ecommerce-store\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\New folder (11)\\\\ecommerce-store\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                    lineNumber: 108,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"md:hidden pb-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSearch,\n                        className: \"relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                value: searchQuery,\n                                onChange: (e)=>setSearchQuery(e.target.value),\n                                placeholder: \"Search products...\",\n                                className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200\",\n                                \"aria-label\": \"Search products on mobile\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\New folder (11)\\\\ecommerce-store\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                className: \"absolute right-3 top-2.5 text-gray-400 hover:text-blue-600 transition-colors\",\n                                \"aria-label\": \"Search\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Search_ShoppingCart_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\New folder (11)\\\\ecommerce-store\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\New folder (11)\\\\ecommerce-store\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\New folder (11)\\\\ecommerce-store\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\New folder (11)\\\\ecommerce-store\\\\src\\\\app\\\\components\\\\Header.tsx\",\n                    lineNumber: 147,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\New folder (11)\\\\ecommerce-store\\\\src\\\\app\\\\components\\\\Header.tsx\",\n            lineNumber: 45,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\New folder (11)\\\\ecommerce-store\\\\src\\\\app\\\\components\\\\Header.tsx\",\n        lineNumber: 44,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Header, \"Uiso29POr7cElPhRc7rtnorTsQk=\", false, function() {\n    return [\n        _context_CartContext__WEBPACK_IMPORTED_MODULE_4__.useCart,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname\n    ];\n});\n_c = Header;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Header);\nvar _c;\n$RefreshReg$(_c, \"Header\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/components/Header.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"18251e6ef556\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGRlZXBzXFxPbmVEcml2ZVxcRGVza3RvcFxcTmV3IGZvbGRlciAoMTEpXFxlY29tbWVyY2Utc3RvcmVcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjE4MjUxZTZlZjU1NlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ })

});