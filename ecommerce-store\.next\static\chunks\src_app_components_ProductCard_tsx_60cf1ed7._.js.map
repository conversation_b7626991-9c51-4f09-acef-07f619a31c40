{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%2811%29/ecommerce-store/src/app/components/ProductCard.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport Image from 'next/image';\n// import { Star, ShoppingCart } from 'lucide-react';\nimport { Product } from '../types';\nimport { useCart } from '../context/CartContext';\nimport { useToast } from '../context/ToastContext';\n\ninterface ProductCardProps {\n  product: Product;\n}\n\nconst ProductCard: React.FC<ProductCardProps> = ({ product }) => {\n  const { addToCart } = useCart();\n  const { showToast } = useToast();\n  const [isAdding, setIsAdding] = React.useState(false);\n\n  const handleAddToCart = React.useCallback((e: React.MouseEvent) => {\n    e.preventDefault();\n    e.stopPropagation();\n    setIsAdding(true);\n    addToCart(product);\n\n    // Show toast notification\n    showToast(`${product.name} added to cart!`, 'success', 2000);\n\n    // Reset animation state after animation completes\n    setTimeout(() => setIsAdding(false), 600);\n  }, [product, addToCart, showToast]);\n\n  const renderStars = (rating: number) => {\n    const stars = [];\n    const fullStars = Math.floor(rating);\n    const hasHalfStar = rating % 1 !== 0;\n\n    for (let i = 0; i < fullStars; i++) {\n      stars.push(\n        <span key={i} className=\"text-yellow-400\">⭐</span>\n      );\n    }\n\n    if (hasHalfStar) {\n      stars.push(\n        <span key=\"half\" className=\"text-yellow-400 opacity-50\">⭐</span>\n      );\n    }\n\n    const emptyStars = 5 - Math.ceil(rating);\n    for (let i = 0; i < emptyStars; i++) {\n      stars.push(\n        <span key={`empty-${i}`} className=\"text-gray-300\">☆</span>\n      );\n    }\n\n    return stars;\n  };\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-md hover:shadow-xl transition-all duration-300 overflow-hidden group transform hover:-translate-y-1\">\n      <Link href={`/product/${product.id}`} className=\"block\">\n        <div className=\"relative aspect-square overflow-hidden\">\n          <Image\n            src={product.image}\n            alt={product.name}\n            fill\n            className=\"object-cover group-hover:scale-110 transition-transform duration-500\"\n            sizes=\"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw\"\n            priority={false}\n          />\n          {product.stock < 10 && product.stock > 0 && (\n            <div className=\"absolute top-2 left-2 bg-orange-500 text-white px-2 py-1 rounded-full text-xs font-medium animate-pulse\">\n              Only {product.stock} left\n            </div>\n          )}\n          {product.stock === 0 && (\n            <div className=\"absolute top-2 left-2 bg-red-500 text-white px-2 py-1 rounded-full text-xs font-medium\">\n              Out of Stock\n            </div>\n          )}\n          <div className=\"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 transition-all duration-300\" />\n        </div>\n      </Link>\n\n      <div className=\"p-4\">\n        <Link href={`/product/${product.id}`} className=\"block\">\n          <h3 className=\"font-semibold text-gray-800 mb-2 line-clamp-2 group-hover:text-blue-600 transition-colors duration-200\">\n            {product.name}\n          </h3>\n\n          <p className=\"text-gray-600 text-sm mb-3 line-clamp-2\">\n            {product.description}\n          </p>\n\n          <div className=\"flex items-center mb-3\">\n            <div className=\"flex items-center\" role=\"img\" aria-label={`Rating: ${product.rating} out of 5 stars`}>\n              {renderStars(product.rating)}\n            </div>\n            <span className=\"text-sm text-gray-500 ml-2\">\n              ({product.reviews} reviews)\n            </span>\n          </div>\n        </Link>\n\n        <div className=\"flex items-center justify-between\">\n          <span className=\"text-lg font-bold text-blue-600\">\n            ${product.price.toFixed(2)}\n          </span>\n\n          <button\n            type=\"button\"\n            onClick={handleAddToCart}\n            disabled={product.stock === 0}\n            className={`bg-blue-600 text-white px-3 py-1.5 rounded-md hover:bg-blue-700 active:bg-blue-800 transition-all duration-200 disabled:bg-gray-400 disabled:cursor-not-allowed flex items-center space-x-1.5 text-sm font-medium transform hover:scale-105 active:scale-95 shadow-md hover:shadow-lg ${\n              isAdding ? 'animate-cart-bounce bg-green-600' : ''\n            }`}\n            aria-label={`Add ${product.name} to cart`}\n          >\n            <span className={`text-sm transition-all duration-200 ${isAdding ? 'animate-cart-shake' : ''}`}>🛒</span>\n            <span className={isAdding ? 'animate-pulse' : ''}>{isAdding ? 'Added!' : 'Add'}</span>\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default React.memo(ProductCard);\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAGA;AACA;;;AARA;;;;;;AAcA,MAAM,cAA0C,CAAC,EAAE,OAAO,EAAE;;IAC1D,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,UAAO,AAAD;IAC5B,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,yIAAA,CAAA,WAAQ,AAAD;IAC7B,MAAM,CAAC,UAAU,YAAY,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAE/C,MAAM,kBAAkB,6JAAA,CAAA,UAAK,CAAC,WAAW;oDAAC,CAAC;YACzC,EAAE,cAAc;YAChB,EAAE,eAAe;YACjB,YAAY;YACZ,UAAU;YAEV,0BAA0B;YAC1B,UAAU,GAAG,QAAQ,IAAI,CAAC,eAAe,CAAC,EAAE,WAAW;YAEvD,kDAAkD;YAClD;4DAAW,IAAM,YAAY;2DAAQ;QACvC;mDAAG;QAAC;QAAS;QAAW;KAAU;IAElC,MAAM,cAAc,CAAC;QACnB,MAAM,QAAQ,EAAE;QAChB,MAAM,YAAY,KAAK,KAAK,CAAC;QAC7B,MAAM,cAAc,SAAS,MAAM;QAEnC,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,IAAK;YAClC,MAAM,IAAI,eACR,6LAAC;gBAAa,WAAU;0BAAkB;eAA/B;;;;;QAEf;QAEA,IAAI,aAAa;YACf,MAAM,IAAI,eACR,6LAAC;gBAAgB,WAAU;0BAA6B;eAA9C;;;;;QAEd;QAEA,MAAM,aAAa,IAAI,KAAK,IAAI,CAAC;QACjC,IAAK,IAAI,IAAI,GAAG,IAAI,YAAY,IAAK;YACnC,MAAM,IAAI,eACR,6LAAC;gBAAwB,WAAU;0BAAgB;eAAxC,CAAC,MAAM,EAAE,GAAG;;;;;QAE3B;QAEA,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,+JAAA,CAAA,UAAI;gBAAC,MAAM,CAAC,SAAS,EAAE,QAAQ,EAAE,EAAE;gBAAE,WAAU;0BAC9C,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,gIAAA,CAAA,UAAK;4BACJ,KAAK,QAAQ,KAAK;4BAClB,KAAK,QAAQ,IAAI;4BACjB,IAAI;4BACJ,WAAU;4BACV,OAAM;4BACN,UAAU;;;;;;wBAEX,QAAQ,KAAK,GAAG,MAAM,QAAQ,KAAK,GAAG,mBACrC,6LAAC;4BAAI,WAAU;;gCAA0G;gCACjH,QAAQ,KAAK;gCAAC;;;;;;;wBAGvB,QAAQ,KAAK,KAAK,mBACjB,6LAAC;4BAAI,WAAU;sCAAyF;;;;;;sCAI1G,6LAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;;0BAInB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,+JAAA,CAAA,UAAI;wBAAC,MAAM,CAAC,SAAS,EAAE,QAAQ,EAAE,EAAE;wBAAE,WAAU;;0CAC9C,6LAAC;gCAAG,WAAU;0CACX,QAAQ,IAAI;;;;;;0CAGf,6LAAC;gCAAE,WAAU;0CACV,QAAQ,WAAW;;;;;;0CAGtB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;wCAAoB,MAAK;wCAAM,cAAY,CAAC,QAAQ,EAAE,QAAQ,MAAM,CAAC,eAAe,CAAC;kDACjG,YAAY,QAAQ,MAAM;;;;;;kDAE7B,6LAAC;wCAAK,WAAU;;4CAA6B;4CACzC,QAAQ,OAAO;4CAAC;;;;;;;;;;;;;;;;;;;kCAKxB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;;oCAAkC;oCAC9C,QAAQ,KAAK,CAAC,OAAO,CAAC;;;;;;;0CAG1B,6LAAC;gCACC,MAAK;gCACL,SAAS;gCACT,UAAU,QAAQ,KAAK,KAAK;gCAC5B,WAAW,CAAC,sRAAsR,EAChS,WAAW,qCAAqC,IAChD;gCACF,cAAY,CAAC,IAAI,EAAE,QAAQ,IAAI,CAAC,QAAQ,CAAC;;kDAEzC,6LAAC;wCAAK,WAAW,CAAC,oCAAoC,EAAE,WAAW,uBAAuB,IAAI;kDAAE;;;;;;kDAChG,6LAAC;wCAAK,WAAW,WAAW,kBAAkB;kDAAK,WAAW,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMrF;GAhHM;;QACkB,wIAAA,CAAA,UAAO;QACP,yIAAA,CAAA,WAAQ;;;KAF1B;2DAkHS,6JAAA,CAAA,UAAK,CAAC,IAAI,CAAC", "debugId": null}}]}