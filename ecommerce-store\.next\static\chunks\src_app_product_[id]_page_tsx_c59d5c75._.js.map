{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%2811%29/ecommerce-store/src/app/product/%5Bid%5D/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { useParams } from 'next/navigation';\nimport Image from 'next/image';\n// import { Star, ShoppingCart, Minus, Plus } from 'lucide-react';\nimport { products } from '../../data/products';\nimport { useCart } from '../../context/CartContext';\n\nexport default function ProductDetail() {\n  const params = useParams();\n  const { addToCart } = useCart();\n  const [quantity, setQuantity] = useState(1);\n\n  const product = products.find(p => p.id === params.id);\n\n  if (!product) {\n    return (\n      <div className=\"container mx-auto px-4 py-8\">\n        <div className=\"text-center\">\n          <h1 className=\"text-2xl font-bold text-gray-800 mb-4\">Product Not Found</h1>\n          <p className=\"text-gray-600\">The product you're looking for doesn't exist.</p>\n        </div>\n      </div>\n    );\n  }\n\n  const handleAddToCart = () => {\n    addToCart(product, quantity);\n  };\n\n  const handleQuantityChange = (change: number) => {\n    const newQuantity = quantity + change;\n    if (newQuantity >= 1 && newQuantity <= product.stock) {\n      setQuantity(newQuantity);\n    }\n  };\n\n  const renderStars = (rating: number) => {\n    const stars = [];\n    const fullStars = Math.floor(rating);\n    const hasHalfStar = rating % 1 !== 0;\n\n    for (let i = 0; i < fullStars; i++) {\n      stars.push(\n        <span key={i} className=\"text-yellow-400 text-lg\">⭐</span>\n      );\n    }\n\n    if (hasHalfStar) {\n      stars.push(\n        <span key=\"half\" className=\"text-yellow-400 opacity-50 text-lg\">⭐</span>\n      );\n    }\n\n    const emptyStars = 5 - Math.ceil(rating);\n    for (let i = 0; i < emptyStars; i++) {\n      stars.push(\n        <span key={`empty-${i}`} className=\"text-gray-300 text-lg\">☆</span>\n      );\n    }\n\n    return stars;\n  };\n\n  return (\n    <div className=\"container mx-auto px-4 py-8\">\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12\">\n        {/* Product Image */}\n        <div className=\"aspect-square relative overflow-hidden rounded-lg bg-gray-100\">\n          <Image\n            src={product.image}\n            alt={product.name}\n            fill\n            className=\"object-cover\"\n          />\n        </div>\n\n        {/* Product Info */}\n        <div className=\"space-y-6\">\n          <div>\n            <h1 className=\"text-3xl font-bold text-gray-800 mb-2\">{product.name}</h1>\n            <p className=\"text-gray-600\">{product.category}</p>\n          </div>\n\n          <div className=\"flex items-center space-x-4\">\n            <div className=\"flex items-center\">\n              {renderStars(product.rating)}\n            </div>\n            <span className=\"text-gray-600\">\n              {product.rating} ({product.reviews} reviews)\n            </span>\n          </div>\n\n          <div className=\"text-3xl font-bold text-blue-600\">\n            ${product.price.toFixed(2)}\n          </div>\n\n          <p className=\"text-gray-700 leading-relaxed\">{product.description}</p>\n\n          <div className=\"space-y-6\">\n            <div className=\"flex items-center space-x-4\">\n              <span className=\"font-medium text-gray-700\">Quantity:</span>\n              <div className=\"flex items-center border border-gray-300 rounded-lg overflow-hidden\">\n                <button\n                  type=\"button\"\n                  onClick={() => handleQuantityChange(-1)}\n                  disabled={quantity <= 1}\n                  className=\"p-2 hover:bg-gray-100 active:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-150 transform active:scale-95\"\n                  aria-label=\"Decrease quantity\"\n                >\n                  <span className=\"text-sm\">−</span>\n                </button>\n                <span className=\"px-4 py-2 font-medium min-w-[3rem] text-center\" aria-label={`Quantity: ${quantity}`}>\n                  {quantity}\n                </span>\n                <button\n                  type=\"button\"\n                  onClick={() => handleQuantityChange(1)}\n                  disabled={quantity >= product.stock}\n                  className=\"p-2 hover:bg-gray-100 active:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-150 transform active:scale-95\"\n                  aria-label=\"Increase quantity\"\n                >\n                  <span className=\"text-sm\">+</span>\n                </button>\n              </div>\n            </div>\n\n            <div className=\"text-sm text-gray-600\">\n              {product.stock > 0 ? (\n                <span className={product.stock < 10 ? 'text-orange-600 font-medium' : ''}>\n                  {product.stock < 10 ? `Only ${product.stock} left in stock` : 'In stock'}\n                </span>\n              ) : (\n                <span className=\"text-red-600 font-medium\">Out of stock</span>\n              )}\n            </div>\n\n            <button\n              type=\"button\"\n              onClick={handleAddToCart}\n              disabled={product.stock === 0}\n              className=\"w-full bg-blue-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-blue-700 active:bg-blue-800 transition-all duration-200 disabled:bg-gray-400 disabled:cursor-not-allowed flex items-center justify-center space-x-2 transform hover:scale-105 active:scale-95 shadow-md hover:shadow-lg\"\n              aria-label={`Add ${quantity} ${product.name} to cart`}\n            >\n              <span className=\"text-lg\">🛒</span>\n              <span>Add to Cart</span>\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA,kEAAkE;AAClE;AACA;;;AAPA;;;;;;AASe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,UAAO,AAAD;IAC5B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,MAAM,UAAU,iIAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,OAAO,EAAE;IAErD,IAAI,CAAC,SAAS;QACZ,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,MAAM,kBAAkB;QACtB,UAAU,SAAS;IACrB;IAEA,MAAM,uBAAuB,CAAC;QAC5B,MAAM,cAAc,WAAW;QAC/B,IAAI,eAAe,KAAK,eAAe,QAAQ,KAAK,EAAE;YACpD,YAAY;QACd;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,MAAM,QAAQ,EAAE;QAChB,MAAM,YAAY,KAAK,KAAK,CAAC;QAC7B,MAAM,cAAc,SAAS,MAAM;QAEnC,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,IAAK;YAClC,MAAM,IAAI,eACR,6LAAC;gBAAa,WAAU;0BAA0B;eAAvC;;;;;QAEf;QAEA,IAAI,aAAa;YACf,MAAM,IAAI,eACR,6LAAC;gBAAgB,WAAU;0BAAqC;eAAtD;;;;;QAEd;QAEA,MAAM,aAAa,IAAI,KAAK,IAAI,CAAC;QACjC,IAAK,IAAI,IAAI,GAAG,IAAI,YAAY,IAAK;YACnC,MAAM,IAAI,eACR,6LAAC;gBAAwB,WAAU;0BAAwB;eAAhD,CAAC,MAAM,EAAE,GAAG;;;;;QAE3B;QAEA,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;wBACJ,KAAK,QAAQ,KAAK;wBAClB,KAAK,QAAQ,IAAI;wBACjB,IAAI;wBACJ,WAAU;;;;;;;;;;;8BAKd,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAyC,QAAQ,IAAI;;;;;;8CACnE,6LAAC;oCAAE,WAAU;8CAAiB,QAAQ,QAAQ;;;;;;;;;;;;sCAGhD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACZ,YAAY,QAAQ,MAAM;;;;;;8CAE7B,6LAAC;oCAAK,WAAU;;wCACb,QAAQ,MAAM;wCAAC;wCAAG,QAAQ,OAAO;wCAAC;;;;;;;;;;;;;sCAIvC,6LAAC;4BAAI,WAAU;;gCAAmC;gCAC9C,QAAQ,KAAK,CAAC,OAAO,CAAC;;;;;;;sCAG1B,6LAAC;4BAAE,WAAU;sCAAiC,QAAQ,WAAW;;;;;;sCAEjE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAA4B;;;;;;sDAC5C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,MAAK;oDACL,SAAS,IAAM,qBAAqB,CAAC;oDACrC,UAAU,YAAY;oDACtB,WAAU;oDACV,cAAW;8DAEX,cAAA,6LAAC;wDAAK,WAAU;kEAAU;;;;;;;;;;;8DAE5B,6LAAC;oDAAK,WAAU;oDAAiD,cAAY,CAAC,UAAU,EAAE,UAAU;8DACjG;;;;;;8DAEH,6LAAC;oDACC,MAAK;oDACL,SAAS,IAAM,qBAAqB;oDACpC,UAAU,YAAY,QAAQ,KAAK;oDACnC,WAAU;oDACV,cAAW;8DAEX,cAAA,6LAAC;wDAAK,WAAU;kEAAU;;;;;;;;;;;;;;;;;;;;;;;8CAKhC,6LAAC;oCAAI,WAAU;8CACZ,QAAQ,KAAK,GAAG,kBACf,6LAAC;wCAAK,WAAW,QAAQ,KAAK,GAAG,KAAK,gCAAgC;kDACnE,QAAQ,KAAK,GAAG,KAAK,CAAC,KAAK,EAAE,QAAQ,KAAK,CAAC,cAAc,CAAC,GAAG;;;;;6DAGhE,6LAAC;wCAAK,WAAU;kDAA2B;;;;;;;;;;;8CAI/C,6LAAC;oCACC,MAAK;oCACL,SAAS;oCACT,UAAU,QAAQ,KAAK,KAAK;oCAC5B,WAAU;oCACV,cAAY,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,QAAQ,IAAI,CAAC,QAAQ,CAAC;;sDAErD,6LAAC;4CAAK,WAAU;sDAAU;;;;;;sDAC1B,6LAAC;sDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpB;GAhJwB;;QACP,qIAAA,CAAA,YAAS;QACF,wIAAA,CAAA,UAAO;;;KAFP", "debugId": null}}]}