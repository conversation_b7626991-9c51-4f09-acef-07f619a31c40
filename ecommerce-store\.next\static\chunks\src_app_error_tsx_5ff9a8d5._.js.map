{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/New%20folder%20%2811%29/ecommerce-store/src/app/error.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\n// import { AlertTriangle, RefreshCw, Home } from 'lucide-react';\n\ninterface ErrorProps {\n  error: Error & { digest?: string };\n  reset: () => void;\n}\n\nexport default function Error({ error, reset }: ErrorProps) {\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gray-50 px-4\">\n      <div className=\"max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center\">\n        <div className=\"text-6xl mb-4\">⚠️</div>\n        <h1 className=\"text-2xl font-bold text-gray-800 mb-2\">Oops! Something went wrong</h1>\n        <p className=\"text-gray-600 mb-6\">\n          We're sorry, but something unexpected happened. Please try again.\n        </p>\n        <div className=\"space-y-3\">\n          <button\n            type=\"button\"\n            onClick={reset}\n            className=\"w-full bg-blue-600 text-white py-2 px-4 rounded-lg font-medium hover:bg-blue-700 active:bg-blue-800 transition-all duration-200 flex items-center justify-center space-x-2 transform hover:scale-105 active:scale-95\"\n          >\n            <span>🔄 Try Again</span>\n          </button>\n          <button\n            type=\"button\"\n            onClick={() => window.location.href = '/'}\n            className=\"w-full bg-gray-200 text-gray-800 py-2 px-4 rounded-lg font-medium hover:bg-gray-300 active:bg-gray-400 transition-all duration-200 flex items-center justify-center space-x-2 transform hover:scale-105 active:scale-95\"\n          >\n            <span>🏠 Go to Homepage</span>\n          </button>\n        </div>\n        {process.env.NODE_ENV === 'development' && error && (\n          <details className=\"mt-6 text-left\">\n            <summary className=\"cursor-pointer text-sm text-gray-500 hover:text-gray-700\">\n              Error Details (Development)\n            </summary>\n            <pre className=\"mt-2 text-xs text-red-600 bg-red-50 p-3 rounded overflow-auto max-h-32\">\n              {error.message}\n            </pre>\n          </details>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;AAmCS;;AAnCT;;AAUe,SAAS,MAAM,EAAE,KAAK,EAAE,KAAK,EAAc;IACxD,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BAAgB;;;;;;8BAC/B,6LAAC;oBAAG,WAAU;8BAAwC;;;;;;8BACtD,6LAAC;oBAAE,WAAU;8BAAqB;;;;;;8BAGlC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,MAAK;4BACL,SAAS;4BACT,WAAU;sCAEV,cAAA,6LAAC;0CAAK;;;;;;;;;;;sCAER,6LAAC;4BACC,MAAK;4BACL,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;4BACtC,WAAU;sCAEV,cAAA,6LAAC;0CAAK;;;;;;;;;;;;;;;;;gBAGT,oDAAyB,iBAAiB,uBACzC,6LAAC;oBAAQ,WAAU;;sCACjB,6LAAC;4BAAQ,WAAU;sCAA2D;;;;;;sCAG9E,6LAAC;4BAAI,WAAU;sCACZ,MAAM,OAAO;;;;;;;;;;;;;;;;;;;;;;;AAO5B;KAtCwB", "debugId": null}}]}