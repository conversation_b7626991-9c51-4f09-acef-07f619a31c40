{"name": "@typescript-eslint/type-utils", "version": "8.36.0", "description": "Type utilities for working with TypeScript + ESLint together", "files": ["dist", "!*.tsbuil<PERSON><PERSON>", "package.json", "README.md", "LICENSE"], "type": "commonjs", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "repository": {"type": "git", "url": "https://github.com/typescript-eslint/typescript-eslint.git", "directory": "packages/type-utils"}, "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "homepage": "https://typescript-eslint.io", "license": "MIT", "keywords": ["eslint", "typescript", "estree"], "scripts": {"build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "typecheck": "yarn run -BT nx typecheck"}, "dependencies": {"@typescript-eslint/typescript-estree": "8.36.0", "@typescript-eslint/utils": "8.36.0", "debug": "^4.3.4", "ts-api-utils": "^2.1.0"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0", "typescript": ">=4.8.4 <5.9.0"}, "devDependencies": {"@typescript-eslint/parser": "8.36.0", "@vitest/coverage-v8": "^3.1.3", "ajv": "^6.12.6", "eslint": "*", "rimraf": "*", "typescript": "*", "vitest": "^3.1.3"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "nx": {"name": "type-utils", "includedScripts": ["clean"], "targets": {"lint": {"command": "eslint"}}}}